# 课时扣减逻辑优化方案

## 问题描述

当前系统存在课时重复扣减的问题：

1. **线上补课扣减**：学生在课程未结束时进行线上补课，系统会扣减一次课时
2. **批量考勤扣减**：后续在课中进行批量考勤时，系统又会再次扣减课时
3. **结果**：同一节课被重复扣减课时，造成逻辑错误

## 问题根源分析

### 现有扣减机制

1. **线上补课扣减**：
   - 位置：`BCourseMakeUpOnlineServiceImpl.markAsAttended()`
   - 使用线上补课记录ID作为timetableId进行扣减
   - 调用：`classTimeStudentManager.deductCourseHours(studentId, makeUpOnline.getId(), courseTypeId)`

2. **批量考勤扣减**：
   - 位置：`BClassTimeStudentServiceImpl` 考勤逻辑
   - 使用原课次的timetableId进行扣减
   - 判断条件：`needDeductCourseHours = isReissue || checkInStatus == null || checkInStatus == 0`

3. **现有防重复机制**：
   - 位置：`CourseHoursPayServiceImpl.reduction()`
   - 通过检查`StoreCourseHoursLog`表防止同一timetableId重复扣减
   - **局限性**：线上补课和批量考勤使用不同的timetableId，无法识别重复扣减

## 优化方案

### 核心思路

在批量考勤的课时扣减判断逻辑中，增加检查学生是否已通过线上补课扣减过课时的条件。

### 具体实现

#### 1. 修改课时扣减判断逻辑

**文件**：`BClassTimeStudentServiceImpl.java`

**位置**：第1335-1340行的`needDeductCourseHours`判断逻辑

**修改内容**：
```java
// 6. 检查是否需要扣减课时
boolean needDeductCourseHours =
    isReissue
        || classTimeStudent.getCheckInStatus() == null
        || classTimeStudent.getCheckInStatus()
        .equals(CheckInStatusEnum.CHECK_IN_STATUS_0.code);

// 6.1 检查学生是否已通过线上补课扣减过课时，避免重复扣减
if (needDeductCourseHours && !isReissue) {
    boolean hasOnlineMakeUpDeduction = checkStudentOnlineMakeUpDeduction(studentId, lessonNo, storeId);
    if (hasOnlineMakeUpDeduction) {
        log.info("学生已通过线上补课扣减课时，跳过批量考勤扣减, studentId: {}, lessonNo: {}", 
            studentId, lessonNo);
        needDeductCourseHours = false;
    }
}
```

#### 2. 新增检查方法

**方法名**：`checkStudentOnlineMakeUpDeduction`

**功能**：检查学生是否已通过线上补课扣减过课时

**实现逻辑**：
1. 查询该课次的所有线上补课记录
2. 遍历每个线上补课记录，检查学生是否已签到
3. 如果发现学生在任何线上补课中已签到，返回true
4. 异常情况下返回false，确保不影响正常业务流程

```java
private boolean checkStudentOnlineMakeUpDeduction(Long studentId, Long lessonNo, Long storeId) {
    try {
        // 1. 查询该课次的所有线上补课记录
        List<BCourseMakeUpOnline> makeUpOnlineList = bCourseMakeUpOnlineMapper.selectList(
            Wrappers.lambdaQuery(BCourseMakeUpOnline.class)
                .eq(BCourseMakeUpOnline::getLessonNo, lessonNo)
                .eq(BCourseMakeUpOnline::getStoreId, storeId)
        );
        
        if (CollectionUtils.isEmpty(makeUpOnlineList)) {
            return false;
        }
        
        // 2. 检查学生是否在任何线上补课中已签到
        for (BCourseMakeUpOnline makeUpOnline : makeUpOnlineList) {
            String makeUpLessonNo = CourseTypeEnum.COURSE_TYPE_ENUM_4.code
                + makeUpOnline.getLessonOrder().toString()
                + makeUpOnline.getId().toString();
            
            long checkInCount = this.count(
                Wrappers.lambdaQuery(BClassTimeStudent.class)
                    .eq(BClassTimeStudent::getStudentId, studentId)
                    .eq(BClassTimeStudent::getLessonNo, Long.valueOf(makeUpLessonNo))
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .eq(BClassTimeStudent::getCheckInStatus, CheckInStatusEnum.CHECK_IN_STATUS_1.code)
            );
            
            if (checkInCount > 0) {
                return true;
            }
        }
        
        return false;
        
    } catch (Exception e) {
        log.error("检查学生线上补课扣减状态时发生异常", e);
        return false; // 异常情况下允许正常扣减流程
    }
}
```

## 优化效果

### 解决的问题

1. **避免重复扣减**：确保每节课的课时只被扣减一次
2. **保持业务完整性**：不影响现有的补签、正常考勤等功能
3. **异常安全**：异常情况下不会阻断正常业务流程

### 适用场景

1. **线上补课后批量考勤**：学生先进行线上补课，后续批量考勤时不会重复扣减
2. **补签操作**：补签操作仍然正常扣减课时（通过`!isReissue`条件排除）
3. **正常考勤流程**：未进行线上补课的学生正常扣减课时

### 技术特点

1. **最小化改动**：只在关键判断点增加检查逻辑
2. **向后兼容**：不影响现有功能和数据结构
3. **性能友好**：只在需要时进行额外查询
4. **日志完善**：提供详细的日志记录便于问题排查

## 测试建议

1. **单元测试**：验证`checkStudentOnlineMakeUpDeduction`方法的各种场景
2. **集成测试**：测试线上补课+批量考勤的完整流程
3. **回归测试**：确保现有考勤功能不受影响
4. **异常测试**：验证异常情况下的处理逻辑

## 部署注意事项

1. **数据备份**：部署前备份相关数据表
2. **灰度发布**：建议先在测试环境验证
3. **监控日志**：部署后关注相关日志输出
4. **回滚准备**：准备快速回滚方案
