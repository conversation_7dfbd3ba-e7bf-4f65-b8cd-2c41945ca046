<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yuedu</groupId>
        <artifactId>ydsf-flow-engine</artifactId>
        <version>5.6.16-SNAPSHOT</version>
    </parent>

    <artifactId>ydsf-flow-engine-api</artifactId>

    <dependencies>
        <!--core 工具类-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-core</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <!--feign 工具类-->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-feign</artifactId>
        </dependency>
        <!-- excel 导入导出 -->
        <dependency>
            <groupId>com.yuedu</groupId>
            <artifactId>ydsf-common-excel</artifactId>
        </dependency>
    </dependencies>
</project>
