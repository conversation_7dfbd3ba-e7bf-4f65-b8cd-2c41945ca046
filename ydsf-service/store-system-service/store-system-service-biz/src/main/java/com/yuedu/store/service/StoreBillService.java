package com.yuedu.store.service;

import com.yuedu.store.dto.StoreDTO;
import com.yuedu.store.vo.StoreBillVO;


/**
 * @ClassName StoreBillService
 * @Description 收费单类
 * <AUTHOR>
 * @Date 2025/05/28 11:40
 * @Version v0.0.1
 */
public interface StoreBillService {

    /**
     * 上传电子章
     *
     * @param
     * @return storeDTO
     */
    Boolean updateBill(StoreDTO storeDTO);
    /**
     * 查看门店电子章
     *
     * @param storeId 学员查询类
     * @return 结果
     */
    StoreBillVO getBillDetail(Long storeId);

}
