package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.extension.mapping.base.MPJDeepService;
import com.yuedu.store.dto.CourseHoursLogDTO;
import com.yuedu.store.dto.StoreCourseHoursLogDTO;
import com.yuedu.store.dto.StudentCourseHoursLogDTO;
import com.yuedu.store.entity.StoreCourseHoursLog;
import com.yuedu.store.query.CourseHoursLogNewQuery;
import com.yuedu.store.query.CourseHoursLogQuery;
import com.yuedu.store.vo.CourseHoursLogNewVO;
import com.yuedu.store.vo.CourseHoursLogTotalVO;
import com.yuedu.store.vo.CourseHoursLogVO;
import com.yuedu.store.vo.StoreCourseHoursLogVO;

import java.util.List;


public interface StoreCourseHoursLogService extends MPJDeepService<StoreCourseHoursLog> {

    /**
     * 通过学生id查询课次记录
     * @param page 分页参数
     * @param studentId 学生id
     * @return Page<CourseHoursLogVO>
     */
    Page<CourseHoursLogVO> getByStudentId(Page<StoreCourseHoursLog> page, Long studentId);


    /**
     *  获得课消更加课次
     *
     * <AUTHOR>
     * @date 2025年03月11日 16时53分
     */
    List<StoreCourseHoursLogVO> getStudentConsumeListByIds(List<Long> ids);

    /**
     * 查询课消记录
     * @param courseHoursLogQuery
     * @return List<CourseHoursLogDTO>
     */
    List<StoreCourseHoursLogDTO> listCourseHoursLog(CourseHoursLogQuery courseHoursLogQuery);

    /**
     * 课消报表
     * @param courseHoursLogNewQuery
     * @return List<CourseHoursLogNewVO>
     */
    Page<CourseHoursLogNewVO> getCourseList(Page page,CourseHoursLogNewQuery courseHoursLogNewQuery);

    /**
     * 课消合计
     * @param courseHoursLogNewQuery
     * @return CourseHoursLogNewVO
     */
    CourseHoursLogTotalVO getCourseAll(CourseHoursLogNewQuery courseHoursLogNewQuery);

    /**
     * 课消明细导出
     * @param courseHoursLogNewQuery
     * @return CourseHoursLogNewVO
     */
    List<CourseHoursLogNewVO> getCourseListExport(CourseHoursLogNewQuery courseHoursLogNewQuery);

    StudentCourseHoursLogDTO getByFtUserId(Long ftUserId);

}
