package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.StoreEmployeeDTO;
import com.yuedu.store.entity.EmployeeAttr;
import com.yuedu.store.vo.StoreEmployeeAttrVO;
import com.yuedu.store.vo.StoreEmployeeVO;


/**
 * 员工附属属性表 服务类
 *
 * <AUTHOR>
 * @date 2024-12-13 09:58:28
 */
public interface EmployeeAttrService extends IService<EmployeeAttr> {


    /**
     * 通过主键查询员工附属属性表
     *
     * @param id 主键
     * @return StoreEmployeeVO
     */
    StoreEmployeeVO getInfoById(Long id);

    /**
     * 修改员工附属属性表
     *
     * @param storeEmployeeDTO 简介/证件照
     * @return Boolean
     */
    Boolean updateInfo(StoreEmployeeDTO storeEmployeeDTO);

    /**
     * 上传头像
     *
     * @param storeEmployeeDTO 头像链接
     * @return Boolean
     */
    Boolean updateAvatar(StoreEmployeeDTO storeEmployeeDTO);

    /**
     * 通过主键查询员工附属属性表
     *
     * @param id 主键
     * @return StoreEmployeeAttrVO
     */
    StoreEmployeeAttrVO getAttrById(Long id);
}
