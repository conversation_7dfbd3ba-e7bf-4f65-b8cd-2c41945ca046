package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.entity.XgjStudent;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface StudentMigrateService {


    void migrateStudentAndCourseHours(String param);

    void syncJob(List<Long> storeIds, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 分页查询
     *
     * @param pageSize    分页大小
     * @param params 查询条件
     * @return 分页结果
     */
    Page<XgjStudent> pageQuery(Page<XgjStudent> pageSize, XgjStudent params);


    void save(List<XgjStudent> params, Long storeId, Long schoolId);


    void sync(Long userId);

    void brushPay(Long batchNo);
}
