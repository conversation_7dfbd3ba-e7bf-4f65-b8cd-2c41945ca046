package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.store.query.StoreStudentTrackRecordQuery;
import com.yuedu.store.dto.StoreStudentTrackRecordDTO;
import com.yuedu.store.vo.StoreStudentTrackRecordVO;
import com.yuedu.store.entity.StoreStudentTrackRecord;

import java.util.List;

/**
 * 会员跟踪记录服务接口
 *
 * <AUTHOR>
 * @date 2025/06/25
 */
public interface StoreStudentTrackRecordService extends IService<StoreStudentTrackRecord> {


    /**
     * 会员跟踪记录分页查询
     *
     * @param page                         分页对象
     * @param storeStudentTrackRecordQuery 会员跟踪记录
     * @return IPage 分页结果
     */
    IPage page(Page page, StoreStudentTrackRecordQuery storeStudentTrackRecordQuery);


    /**
     * 新增会员跟踪记录
     *
     * @param storeStudentTrackRecordDTO 会员跟踪记录
     * @return boolean 执行结果
     */
    boolean add(StoreStudentTrackRecordDTO storeStudentTrackRecordDTO);


    /**
     * 修改会员跟踪记录
     *
     * @param storeStudentTrackRecordDTO 会员跟踪记录
     * @return boolean 执行结果
     */
    boolean edit(StoreStudentTrackRecordDTO storeStudentTrackRecordDTO);


    /**
     * 导出excel 会员跟踪记录表格
     *
     * @param storeStudentTrackRecordQuery 查询条件
     * @param ids                          导出指定ID
     * @return List<StoreStudentTrackRecordVO> 结果集合
     */
    List<StoreStudentTrackRecordVO> export(StoreStudentTrackRecordQuery storeStudentTrackRecordQuery, Long[] ids);
}
