package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.ClassDTO;
import com.yuedu.store.entity.Class;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.query.ClassQuery;
import com.yuedu.store.vo.ClassStudentListVO;
import com.yuedu.store.vo.ClassVO;

import java.util.List;

/**
 * 班级 服务类
 *
 * <AUTHOR>
 * @date 2024-11-26 14:30:59
 */
public interface ClassService extends IService<Class> {


    /**
     * 根据校区id查询班级信息
     *
     * @param campusQuery 校区id
     * @return List<TClassVO>
     */
    List<ClassVO> getClassByCampusId(CampusQuery campusQuery);

    /**
     * 根据门店id查询班级信息
     *
     * @return List<TClassVO>
     */
    List<ClassVO> getClassByStoreId(ClassQuery classQuery);

    /**
     * 根据门店id查询班级信息分页
     *
     * @return List<TClassVO>
     */
    ClassStudentListVO allClassPage (Page<Class> page, ClassQuery classQuery);

    /**
     * 保存班级
     * <AUTHOR>
     * @date 2025/2/12
     * @param classDTO
     * @return void
     */
    Boolean saveClass(ClassDTO classDTO);


    /**
     * 删除班级
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    void removeClass(ClassDTO classDTO);

    /**
     * 班级详情
     *
     * <AUTHOR>
     * @date 2025/2/14
     */
    ClassVO getDetail(Integer id);

    /**
     * 班级结课
     * <AUTHOR>
     * @date 2025/2/14
     * @param classDTO
     * @return void
     */
    void endClass(ClassDTO classDTO);

}
