package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.entity.Employee;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.vo.EmployeeVO;

import java.util.List;


/**
 * 老师 服务类
 *
 * <AUTHOR>
 * @date 2024-11-26 15:09:08
 */
public interface EmployeeService extends IService<Employee> {

    /**
     * 根据校区ID查询老师列表
     *
     * @param campusQuery 校区ID
     * @return List<TEmployeeVO>
     */
    List<EmployeeVO> getEmployeeByCampusId(CampusQuery campusQuery, Integer status);

    /**
     * 根据手机号查询老师
     *
     * @param appUserQuery 手机号
     * @return StoreEmployee
     */
    Employee getEmployeeByPhone(AppUserQuery appUserQuery);

    /**
     * 根据老师ID列表查询老师列表
     *
     * @param teacherIdList 老师ID列表
     * @return Map<Long, EmployeeVO>
     */
    List<EmployeeVO> getEmployeeListByIdList(List<Long> teacherIdList);
}
