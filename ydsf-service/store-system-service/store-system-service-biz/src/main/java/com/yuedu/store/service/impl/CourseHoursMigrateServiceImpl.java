package com.yuedu.store.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.store.constant.enums.CourseHoursOperationEnum;
import com.yuedu.store.entity.*;
import com.yuedu.store.mapper.*;
import com.yuedu.store.service.CourseHoursMigrateService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


/**
 * 数据迁移
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class CourseHoursMigrateServiceImpl implements CourseHoursMigrateService {

    private final CourseHoursTempMapper courseHoursTempMapper;
    private final CourseHoursRecordMapper courseHoursRecordMapper;
    private final CourseHoursPayMapper courseHoursPayMapper;


    @Override
    public void migrateCourseHoursPay() {
        CourseHoursTemp courseHoursTemp = getCourseHoursBatchNo(0L);
        while (courseHoursTemp != null) {
            // 处理 courseHoursTemp 的逻辑

            //获取批次
            Long batchNo = courseHoursTemp.getBatchNo();
            List<StoreCourseHoursRecord> courseHoursRecord = courseHoursRecordMapper.selectList( Wrappers.<StoreCourseHoursRecord>lambdaQuery()
                    .eq(StoreCourseHoursRecord::getBatchNo, batchNo)
                    .gt(StoreCourseHoursRecord::getCount, 0)
            );

            if (ObjectUtil.isNotEmpty(courseHoursRecord)) {

                //获取所有的item.getOperationType()
                List<Integer> courseTypeIds = courseHoursRecord.stream().map(StoreCourseHoursRecord::getCourseType).distinct().toList();
                if (courseTypeIds.size() == 1) {
                    CourseHoursPay courseHoursPay = new CourseHoursPay();
                    courseHoursPay.setStudentId(courseHoursRecord.get(0).getStudentId());
                    courseHoursPay.setCourseType(courseHoursRecord.get(0).getCourseType());
                    courseHoursPay.setSchoolId(courseHoursRecord.get(0).getSchoolId());
                    courseHoursPay.setStoreId(courseHoursRecord.get(0).getStoreId());
                    courseHoursPay.setId(courseHoursRecord.get(0).getBatchNo());
                    courseHoursPay.setTotalAmount(BigDecimal.ZERO);
                    courseHoursPay.setGift(0);
                    courseHoursPay.setFormal(0);
                    for (StoreCourseHoursRecord item : courseHoursRecord) {
                        if (item.getOperationType().equals(CourseHoursOperationEnum.ENROLL.getDesc())){
                            courseHoursPay.setFormal(courseHoursPay.getFormal() + item.getCount());
                        } else {
                            courseHoursPay.setGift(courseHoursPay.getGift() + item.getCount());
                        }
                        courseHoursPay.setTotalAmount(courseHoursPay.getTotalAmount().add(item.getTotalAmount()));
                    }
                    courseHoursPayMapper.insert(courseHoursPay);
                }
                courseHoursTempMapper.updateById(CourseHoursTemp.builder().id(courseHoursTemp.getId()).status(1).build());
            }

            // 获取下一条数据
            courseHoursTemp = getCourseHoursBatchNo(courseHoursTemp.getId());
        }
    }

    private CourseHoursTemp getCourseHoursBatchNo(Long id) {
        //获取studentMapper数据
        return courseHoursTempMapper.selectOne(Wrappers.<CourseHoursTemp>lambdaQuery()
                .eq(CourseHoursTemp::getStatus, 0)
                .gt(CourseHoursTemp::getId, id)
                .orderByAsc(CourseHoursTemp::getId)
                .last("limit 1")
        );
    }
}