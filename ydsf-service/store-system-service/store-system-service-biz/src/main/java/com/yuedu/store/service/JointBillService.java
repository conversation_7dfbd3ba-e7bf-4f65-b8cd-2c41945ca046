package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.entity.JointBill;
import com.yuedu.store.query.JointBillQuery;
import com.yuedu.store.vo.JointBillVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface JointBillService extends IService<JointBill> {

    /**
     * 分页查询
     *
     * @param pageSize    分页大小
     * @param jointBillQuery 查询条件
     * @return 分页结果
     */
    Page<JointBillVO> pageQuery(Page<JointBill> pageSize, JointBillQuery jointBillQuery);

    /**
     * 同步财务联营收费单
     */
    void syncJointBill(List<Long> storeIds, LocalDateTime startDate, LocalDateTime endDate);

    void syncJointBillByBatchNo(List<Long> batchNoList);

    void syncJointBillByStatus();
}