package com.yuedu.store.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.EmployeeCampusDTO;
import com.yuedu.store.dto.EmployeeCampusNewDTO;
import com.yuedu.store.entity.EmployeeCampus;
import com.yuedu.store.query.EmployeeCampusQuery;
import com.yuedu.store.vo.EmployeeCampusInfoVO;
import com.yuedu.store.vo.EmployeeVO;

import java.util.List;

public interface EmployeeCampusService extends IService<EmployeeCampus> {


    IPage listSchoolEmployeeCampus(Page<EmployeeCampus> pageSize);
    /**
     * 获取员工校区详细信息
     *
     * @param id 员工校区的唯一标识符
     * @return 返回包含员工校区详细信息的EmployeeCampusInfoVO对象
     *
     * 此方法用于根据提供的员工校区ID获取对应的员工校区详细信息。返回的EmployeeCampusInfoVO对象包含了员工校区的各种详细信息，
     * 如校区名称、地址、负责人等。如果未找到对应的员工校区信息，则返回null。
     */
    EmployeeCampusInfoVO getEmployeeCampusDetail(Long id);

    /**
     * 获取员工校区详细信息
     *
     * @return 返回包含员工校区详细信息的EmployeeCampusInfoVO对象
     *
     * 此方法用于根据提供的员工校区ID获取对应的员工校区详细信息。返回的EmployeeCampusInfoVO对象包含了员工校区的各种详细信息，
     * 如校区名称、地址、负责人等。如果未找到对应的员工校区信息，则返回null。
     */
    EmployeeCampusInfoVO getEmployeeCampusDetail(Long userId, Long storeId);


    /**
     * 根据角色获取门店下的员工
     * @param storeId
     * @param roles
     * @return
     */
    List<EmployeeVO> getEmployeeByRoles(Long storeId, String roles);

    /**
     * 更新员工校区状态
     *
     * @param employeeCampusDTO 包含要更新的员工校区状态信息的DTO对象
     *
     * <p>该方法用于更新员工校区的状态。方法接收一个{@link EmployeeCampusDTO}对象作为参数，该对象包含了需要更新的员工校区状态信息。</p>
     *
     * <p>注意：该方法的具体实现细节（如如何更新数据库、处理异常等）未在方法签名中体现，而是在方法体内部实现。调用者只需确保传入正确的DTO对象即可。</p>
     */
    void updateEmployeeCampusStatus(EmployeeCampusDTO employeeCampusDTO);

    /**
     * 更新员工校区信息
     *
     * @param employeeCampusDTO 包含要更新的员工校区信息的DTO对象

     * 该方法用于更新员工校区信息。传入的参数是一个包含要更新信息的DTO（数据传输对象），其中包含了需要更新的员工校区相关字段。
     * 方法的具体实现（如更新哪些字段、如何更新等）未在方法签名中明确，需要在方法体内实现。
     * 注意：由于此方法签名未使用throws关键字声明可能抛出的异常，调用者需要自行处理可能的异常情况，或者在实际的方法实现中添加异常处理逻辑。
     */
    void updateEmployeeCampus(EmployeeCampusDTO employeeCampusDTO);

    /**
     * 添加或更新员工校区信息
     * 该方法根据传入的EmployeeCampusDTO对象中的信息，安装或更新员工的校区信息。
     *
     * @param employeeCampusDTO 包含员工校区信息的DTO对象
     */
    void installEmployeeCampus(EmployeeCampusDTO employeeCampusDTO);

    void saveIdsEmployeeCampus(EmployeeCampusDTO employeeCampusDTO);


    //校区门店
    IPage page(Page page, EmployeeCampusQuery temployeeCampusQuery);

    /**
     * 更新启用状态
     * @param params 参数
     */
    void updateIsEnable(EmployeeCampusNewDTO params);

    /**
     * 更新员工
     * @param params 参数
     */
    void updateEmployeeCampus(EmployeeCampusNewDTO params);

    /**
     * 新增员工
     * @param params 参数
     */
    void addEmployeeCampus(EmployeeCampusNewDTO params);

    /**
     * 根据校区id查询员工
     * @param schoolId 校区id
     * @return 员工列表
     */
    List<EmployeeVO>  getEmployeeBySchoolId(Long schoolId);
}
