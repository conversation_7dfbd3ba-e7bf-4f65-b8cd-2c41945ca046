package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.entity.LecturerEntity;
import com.yuedu.store.vo.LecturerVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LecturerService extends IService<LecturerEntity> {

    /**
     * 查询全部教师
     *
     * @return List<LectureVO>
     */
    List<LecturerVO> listLecturer(String name);

    /**
     * 根据教师idList查询教师
     *
     * @param ids 教师idList
     * @return List<LectureVO>
     */
    List<LecturerVO> selectListByLecturerId(List<Long> ids, String lecturerName);

    /**
     * 根据教师id查询教师
     *
     * @param id 教师id
     * @return LectureVO
     */
    LecturerVO selectByLecturerId(Long id);

    /**
     * 通过模糊查询查询主讲老师信息
     *
     * @param lecturerName 主讲老师姓名
     * @return List<LecturerVO> 主讲老师信息列表
     */
    List<LecturerVO> selectByLecturer(String lecturerName);

    /**
     * 查询所有主讲老师
     * @param lecturerDTO
     * @return java.util.List<com.yuedu.store.vo.LecturerVO>
     * <AUTHOR>
     * @date 2025/2/10 16:47
     */
    List<LecturerVO> getLecturerAll(@Valid LecturerDTO lecturerDTO);

}