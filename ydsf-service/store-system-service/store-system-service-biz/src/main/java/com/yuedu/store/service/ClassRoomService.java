package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.entity.ClassRoomEntity;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.vo.ClassRoomVO;

import java.util.List;
import java.util.Map;

/**
 * 教室
 *
 * <AUTHOR>
 * @date 2024-09-29 10:35:15
 */
public interface ClassRoomService extends IService<ClassRoomEntity> {

    /**
     * 查询全部教室信息
     *
     * @return List<ClassRoomVO>
     */
    List<ClassRoomVO> listClassRoom(String name);

    /**
     * 条件查询教室列表
     *
     * @param campusId 校区id
     * @return List<ClassRoomVO>
     */
    List<ClassRoomVO> queryList(Long campusId);

    /**
     * 条件查询教室列表
     *
     * @param classRoomDTO 教室请求实体
     * @return List<ClassRoomVO>
     */
    List<ClassRoomVO> queryList(ClassRoomDTO classRoomDTO);

    /**
     * 根据id查询教室信息
     *
     * @param classRoomId 教室ID
     * @return ClassRoomVO
     */
    ClassRoomVO queryByClassRoomId(Long classRoomId);

    /**
     * 条件查询
     *
     * @param pageRequest     分页参数
     * @param classRoomEntity 请求实体
     * @return Page<ClassRoomEntity>
     */
    Page<ClassRoomEntity> queryByCondition(Page pageRequest, ClassRoomEntity classRoomEntity);

    /**
     * 根据校区ID查询教室信息
     *
     * @param campusQuery 校区ID
     * @return List<ClassRoomVO>
     */
    List<ClassRoomVO> getClassRoomByCampusId(CampusQuery campusQuery);

    /**
     * 根据教室ID集合查询教室信息
     *
     * @param classroomIdList 教室ID集合
     * @return Map<Long, ClassRoomVO>
     */
    Map<Long, ClassRoomVO> getClassRoomMapByIdList(List<Long> classroomIdList);

    /**
     * 查询所有教室
     * @param classRoomDto
     * @return java.util.List<com.yuedu.store.vo.ClassRoomVO>
     * <AUTHOR>
     * @date 2025/2/10 16:37
     */
    List<ClassRoomVO> getClassRoomList(ClassRoomDTO classRoomDto);


    /**
     * 新增教室
     * @param classRoomDTO 请求实体
     * @return Boolean
     */
    Boolean addClassRoom(ClassRoomDTO classRoomDTO);

    /**
     * 修改教室
     * @param classRoomDTO 请求实体
     * @return Boolean
     */
    Boolean edit(ClassRoomDTO classRoomDTO);

    /**
     * 删除教室
     * @param id id
     * @return Boolean
     */
    Boolean removeClassRoom(Long id);
}
