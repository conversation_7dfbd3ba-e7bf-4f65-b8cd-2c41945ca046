package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.SchoolDTO;
import com.yuedu.store.entity.School;
import com.yuedu.store.query.SchoolQuery;
import com.yuedu.store.vo.SchoolVO;

/**
 * <AUTHOR>
 */
public interface SchoolService extends IService<School> {

    /**
     * 分页查询
     *
     * @param pageSize    分页大小
     * @param schoolQuery 查询条件
     * @return 分页结果
     */
    Page<SchoolVO> pageQuery(Page<School> pageSize, SchoolQuery schoolQuery);

    /**
     * 新增校区
     */
    void addSchool(SchoolDTO school);

    /**
     * 修改校区
     */
    void editSchool(SchoolDTO school);
}