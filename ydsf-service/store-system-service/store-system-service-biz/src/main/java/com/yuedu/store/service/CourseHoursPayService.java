package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.dto.*;
import com.yuedu.store.entity.CourseHoursPay;
import com.yuedu.store.query.CourseHoursRefundQuery;
import com.yuedu.store.query.StoreCourseHoursPayQuery;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.vo.CourseHoursPayVO;
import com.yuedu.store.vo.StoreCourseHoursPayVO;
import com.yuedu.store.vo.StoreRefundRecordVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface CourseHoursPayService extends IService<CourseHoursPay> {

    /**
     * 判断校区是否为联营
     * @param storeId 门店ID
     * @return  Boolean
     */
    boolean isJoint(Long storeId);

    /**
     * 创建联营门店收费单
     */
    void createJointBill(Long batchNo);

    /**
     * 为学员录入课时
     *
     * @param params 课时信息
     */
    void saveCourseHour(CourseHoursPayDTO params);

    /**
     * 给学员全部退费
     *
     * @param schoolId   校区ID
     * @param storeId    门店ID
     * @param studentId  学员ID
     */
    void refundStudent(Long schoolId, Long storeId, Long studentId, LocalDate refundDate);

    /**
     * 获取可退费列表
     * @param params 参数
     * @return List<CourseHoursPayVO>
     */
    List<CourseHoursPayVO> refundList(CourseHoursRefundQuery params);


    /**
     * 给学员部分退费
     *
     * @param params 学员信息
     */
    BigDecimal refundAmount(CoursePartRefundDTO params, Boolean isCompute);


    /**
     * 学员课消
     *
     * @param ridStudent 学员列表
     */
    void reduction(CourseHoursRidDTO ridStudent);

    /**
     * 取消考勤
     *
     * @param params 取消考勤
     */
    void courseHoursCancel(CourseHoursCancelDTO params);


    /**
     * 作废收费单
     *
     * @param params 取消考勤
     */
    void revoke(CourseHoursRevokeDTO params);

    /**、
     * 候补课消
     * @param studentId   学员ID
     * @param courseType  课次类型
     */
    Long alternate(Long studentId, Integer courseType);

    /**
     * 收费报表分页查询
     * @param page
     * @param storeCourseHoursPayQuery
     */
    Page<StoreCourseHoursPayVO> getBillPage(Page page, StoreCourseHoursPayQuery storeCourseHoursPayQuery);

    /**
     * 通过id查询收费信息
     * @param id
     */
    StoreCourseHoursPayVO getBillById(Long id);

    /**
     * 编辑收费单
     *
     * @param params 编辑收费单
     */
    void edit(CourseHoursEditDTO params);
}