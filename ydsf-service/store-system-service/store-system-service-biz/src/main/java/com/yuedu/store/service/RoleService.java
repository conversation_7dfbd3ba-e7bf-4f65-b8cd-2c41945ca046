/*
 *
 *      Copyright (c) 2018-2025, ydsf All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: ydsf
 *
 */

package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.entity.Role;
import com.yuedu.ydsf.admin.api.vo.RoleMenuVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface RoleService extends IService<Role> {


    /**
     * 根据角色ID 查询角色列表
     *
     * @param roleIdList 角色ID列表
     * @param key        缓存key
     * @return
     */
    List<Role> findRolesByRoleIds(List<Long> roleIdList, String key);

    /**
     * 通过角色ID，删除角色
     *
     * @param ids
     * @return
     */
    Boolean removeRoleByIds(Long[] ids);

    /**
     * 根据角色菜单列表
     *
     * @param roleVo 角色&菜单列表
     * @return
     */
    Boolean updateRoleMenus(RoleMenuVO roleVo);

}
