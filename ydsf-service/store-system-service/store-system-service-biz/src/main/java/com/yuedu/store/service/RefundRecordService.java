package com.yuedu.store.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuedu.store.entity.RefundRecord;
import com.yuedu.store.mq.dto.RefundDTO;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.vo.StoreRefundRecordVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 退费记录 服务类
 *
 * <AUTHOR>
 * @date 2025-07-21 14:30:59
 */
public interface RefundRecordService extends IService<RefundRecord> {

    /**
     * 创建退费记录
     */
    void create(RefundDTO refundDto);

    /**
     * 退费报表分页查询
     * @param page
     * @param storeRefundRecordQuery
     */
    Page<StoreRefundRecordVO> getRefundRecordPage(Page page, StoreRefundRecordQuery storeRefundRecordQuery);

}
