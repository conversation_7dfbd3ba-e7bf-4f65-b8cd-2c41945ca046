package com.yuedu.ydsf.eduConnect.jw.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.api.feign.RemoteClassStudentService;
import com.yuedu.store.api.feign.RemoteStoreSettingService;
import com.yuedu.store.api.feign.RemoteStudentService;
import com.yuedu.store.constant.cst.StoreConstant;
import com.yuedu.store.constant.enums.ClassStudentEnum;
import com.yuedu.store.constant.enums.StudentRegularEnum;
import com.yuedu.store.constant.enums.StudentStatusEnum;
import com.yuedu.store.dto.CourseHoursCancelDTO;
import com.yuedu.store.dto.StudentUpdateDTO;
import com.yuedu.store.entity.CourseHoursStudent;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.store.vo.StudentVO;
import com.yuedu.teaching.api.feign.RemoteCourseService;
import com.yuedu.teaching.api.feign.RemoteCourseTypeService;
import com.yuedu.teaching.api.feign.RemoteLessonService;
import com.yuedu.teaching.dto.CourseDTO;
import com.yuedu.teaching.dto.CourseTypeDTO;
import com.yuedu.teaching.dto.LessonOrderDTO;
import com.yuedu.teaching.entity.LessonEntity;
import com.yuedu.teaching.query.CoursePublishQuery;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.teaching.vo.LessonVO;
import com.yuedu.ydsf.common.core.constant.enums.YesNoEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.util.PinyinComparatorUtil;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.data.annotation.ForceMaster;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.common.core.constant.CacheConstants;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInEntryTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.ClickerBindStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CourseTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionSendTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.InteractionTypeEnum;
import com.yuedu.ydsf.eduConnect.api.constant.StudentTypeEnum;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteSettlementCycleService;
import com.yuedu.ydsf.eduConnect.api.query.SettlementCycleQuery;
import com.yuedu.ydsf.eduConnect.api.vo.SettlementCycleVO;
import com.yuedu.ydsf.eduConnect.jw.api.constant.CourseLiveConstant;
import com.yuedu.ydsf.eduConnect.jw.api.constant.JwConstant;
import com.yuedu.ydsf.eduConnect.jw.api.constant.enums.ClassStudentErrorEnum;
import com.yuedu.ydsf.eduConnect.jw.api.dto.AttendanceConfirmationDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.AttendanceConfirmationDTO.OriginalStatusType;
import com.yuedu.ydsf.eduConnect.jw.api.dto.BClassTimeStudentDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.StudentCheckInPreValidationDTO;
import com.yuedu.ydsf.eduConnect.jw.api.dto.TemporaryStudentDTO;
import com.yuedu.ydsf.eduConnect.jw.api.query.BClassTimeStudentQuery;
import com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceConfirmationResultVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceManagementVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BInteractionReceiverVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.BatchBindClickerResultVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.ClassStudentErrorVO;
import com.yuedu.ydsf.eduConnect.jw.api.vo.ClickerUnbindPromptVO;
import com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent;
import com.yuedu.ydsf.eduConnect.jw.entity.BCourseMakeUpOnline;
import com.yuedu.ydsf.eduConnect.jw.entity.BInteractionReceiverClicker;
import com.yuedu.ydsf.eduConnect.jw.entity.CourseMakeUp;
import com.yuedu.ydsf.eduConnect.jw.entity.Timetable;
import com.yuedu.ydsf.eduConnect.jw.entity.TimetableChange;
import com.yuedu.ydsf.eduConnect.jw.manager.ClassTimeStudentManager;
import com.yuedu.ydsf.eduConnect.jw.manager.CourseMakeUpOnlineManager;
import com.yuedu.ydsf.eduConnect.jw.manager.TimetableChangeManager;
import com.yuedu.ydsf.eduConnect.jw.mapper.BClassTimeStudentMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.BCourseMakeUpOnlineMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.CourseMakeUpMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.SsInteractionConsequenceMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableChangeMapper;
import com.yuedu.ydsf.eduConnect.jw.mapper.TimetableMapper;
import com.yuedu.ydsf.eduConnect.jw.service.BClassTimeStudentService;
import com.yuedu.ydsf.eduConnect.jw.service.BInteractionReceiverClickerService;
import com.yuedu.ydsf.eduConnect.jw.proxy.service.ReadingPartyProxyService;
import com.yuedu.ydsf.eduConnect.jw.service.OnlineMakeupNotificationService;
import com.yuedu.ydsf.eduConnect.jw.service.StudentAttendanceNotificationService;
import com.yuedu.ydsf.eduConnect.jw.service.WxStudentMsgService;
import com.yuedu.ydsf.eduConnect.jw.entity.WxStudentMsg;
import com.yuedu.ydsf.eduConnect.jw.api.dto.WxMsgTplDto;
import com.yuedu.ydsf.eduConnect.jw.api.dto.WxMsgTplDto.DataContent;
import com.yuedu.ydsf.eduConnect.jw.api.dto.WxMsgTplDto.DataContent.ValueContent;
import com.yuedu.store.dto.StudentMemberDTO;
import com.yuedu.store.dto.StoreSettingDTO;
import com.yuedu.ydsf.eduConnect.jw.config.JwBusinessConfig;
import com.yuedu.ydsf.eduConnect.jw.service.BInteractionReceiverService;
import com.yuedu.ydsf.eduConnect.live.api.dto.AgoraUpdateRoomPropertiesDTO;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.concurrent.TimeUnit;
import java.time.Duration;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课次学生表服务层
 *
 * <AUTHOR>
 * @date 2025/02/24
 */
@Service
@Slf4j
@AllArgsConstructor
public class BClassTimeStudentServiceImpl
    extends ServiceImpl<BClassTimeStudentMapper, BClassTimeStudent>
    implements BClassTimeStudentService {

    private final TimetableMapper timetableMapper;
    private final RemoteClassStudentService remoteClassStudentService;
    private final RemoteStudentService remoteStudentService;
    private final RemoteLessonService remoteLessonService;
    private final TimetableChangeManager timetableChangeManager;
    private final TimetableChangeMapper timetableChangeMapper;
    private final BInteractionReceiverService bInteractionReceiverService;
    private final BInteractionReceiverClickerService bInteractionReceiverClickerService;
    private final RemoteSettlementCycleService remoteSettlementCycleService;
    private final ClassTimeStudentManager classTimeStudentManager;
    private final BCourseMakeUpOnlineMapper bCourseMakeUpOnlineMapper;
    private final CourseMakeUpOnlineManager courseMakeUpOnlineManager;
    private final CourseMakeUpMapper courseMakeUpMapper;
    private final RemoteCourseService remoteCourseService;
    private final RemoteCourseTypeService remoteCourseTypeService;
    private final SsInteractionConsequenceMapper ssInteractionConsequenceMapper;
    private final BClassTimeStudentMapper bClassTimeStudentMapper;
    private final ReadingPartyProxyService readingPartyProxyService;
    private final WxStudentMsgService wxStudentMsgService;
    private final JwBusinessConfig jwBusinessConfig;
    private final RemoteStoreSettingService remoteStoreSettingService;
    private final StudentAttendanceNotificationService studentAttendanceNotificationService;
    private final OnlineMakeupNotificationService onlineMakeupNotificationService;
    private final RedisTemplate<String, Object> redisTemplate;
    // WeChat notification constants
    private static final String WX_APP_NAME = "约读儿童读书会服务号";
    private static final String WX_APP_ID = "wx99bfe428aee3cef4";
    private static final Integer WX_MSG_TYPE_SEND_TO_USER = 2;
    private static final String WX_MSG_REP_TYPE_TEXT = "text";
    private static final Integer WX_MSG_REP_EVENT_COURSE_REMINDER = 2;
    private static final Integer WX_MSG_SEND_STATUS_SENDED = 1;
    private static final Integer WX_MSG_READ_FLAG_UNREAD = 0;


    /**
     * 课次学生表分页查询
     *
     * @param page                   分页对象
     * @param bClassTimeStudentQuery 课次学生表
     * @return IPage 分页结果
     */
    @Override
    public IPage page(Page page, BClassTimeStudentQuery bClassTimeStudentQuery) {
        return page(page, Wrappers.<BClassTimeStudent>lambdaQuery());
    }

    /**
     * 新增课次学生表
     *
     * @param bClassTimeStudentDTO 课次学生表
     * @return boolean 执行结果
     */
    @Override
    public boolean add(BClassTimeStudentDTO bClassTimeStudentDTO) {
        BClassTimeStudent bClassTimeStudent = new BClassTimeStudent();
        BeanUtils.copyProperties(bClassTimeStudentDTO, bClassTimeStudent);
        return save(bClassTimeStudent);
    }

    /**
     * 修改课次学生表
     *
     * @param bClassTimeStudentDTO 课次学生表
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(BClassTimeStudentDTO bClassTimeStudentDTO) {
        BClassTimeStudent bClassTimeStudent = new BClassTimeStudent();
        BeanUtils.copyProperties(bClassTimeStudentDTO, bClassTimeStudent);
        return updateById(bClassTimeStudent);
    }

    /**
     * 导出excel 课次学生表表格
     *
     * @param bClassTimeStudentQuery 查询条件
     * @param ids                    导出指定ID
     * @return List<BClassTimeStudentVO> 结果集合
     */
    @Override
    public List<BClassTimeStudentVO> export(
        BClassTimeStudentQuery bClassTimeStudentQuery, Long[] ids) {
        return list(
            Wrappers.<BClassTimeStudent>lambdaQuery()
                .in(ArrayUtil.isNotEmpty(ids), BClassTimeStudent::getId, ids)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode()))) // 过滤手动移除的学员
            .stream()
            .map(
                entity -> {
                    BClassTimeStudentVO bClassTimeStudentVO = new BClassTimeStudentVO();
                    BeanUtils.copyProperties(entity, bClassTimeStudentVO);
                    return bClassTimeStudentVO;
                })
            .toList();
    }

    /**
     * 课表签到考勤入口获取学生列表
     *
     * @return com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO
     * <AUTHOR>
     * @date 2025/2/25 10:38
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @ForceMaster
    public CheckInStudentVO getCheckInStudent(
        Serializable lessonNo, Integer entryType, Long storeIdArg) {
        log.info("开始获取课表签到考勤学生列表, lessonNo: {}", lessonNo);

        Long lessonNoLong = Long.valueOf(lessonNo.toString());

        // 1. 查询课表信息，获取班级ID
        Timetable timetable = getTimetableInfo(lessonNoLong, storeIdArg);

        // 判断课程是否已结束
        boolean isClassEnded = false;
        if (timetable.getClassEndDateTime() != null
            && LocalDateTime.now().isAfter(timetable.getClassEndDateTime())) {
            isClassEnded = true;
            log.info("课程已结束，将保留历史出勤记录，不再对比学生状态, lessonNo: {}", lessonNoLong);
        }

        // 签到考勤入口开课前30分钟才允许获取学生列表
        if (Objects.equals(entryType, CheckInEntryTypeEnum.CHECK_IN_ENTRY_TYPE_1.code)) {
            checkTimetableTimeForOperation(timetable, false);
        }

        // 2. 查询课节信息，获取课节名称
        LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
        lessonOrderDTO.setCourseId(timetable.getCourseId());
        lessonOrderDTO.setLessonOrderList(Collections.singletonList(timetable.getLessonOrder()));

        R<List<LessonVO>> lessonResult =
            remoteLessonService.getLessonListByOrder(Collections.singletonList(lessonOrderDTO));
        log.info("查询课节信息结果: {}", JSON.toJSONString(lessonResult));

        String lessonName = "";
        if (lessonResult.isOk() && CollUtil.isNotEmpty(lessonResult.getData())) {
            lessonName = lessonResult.getData().get(0).getLessonName();
        }

        // 3. 查询当前课次已有的学生记录（过滤手动移除的学员）
        List<BClassTimeStudent> existingStudents =
            this.list(
                Wrappers.<BClassTimeStudent>lambdaQuery()
                    .eq(BClassTimeStudent::getLessonNo, lessonNoLong)
                    .eq(BClassTimeStudent::getStoreId, storeIdArg)
                    .ne(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode()))); // 过滤手动移除的学员
        log.info("查询到已有课次学生记录数（已过滤手动移除）: {}", existingStudents.size());

        List<BClassTimeStudent> studentsToAdd = new ArrayList<>();
        List<Long> studentsToDelete = new ArrayList<>();

        // 如果课程未结束，才进行学生状态对比
        if (!isClassEnded) {
            // 4. 获取班级学生列表
            R<List<StudentVO>> classStudentsResult =
                remoteStudentService.getListByClassId(timetable.getClassId().intValue());
            log.debug("查询班级学生结果: {}", JSON.toJSONString(classStudentsResult));

            List<StudentVO> studentVOList = new ArrayList<>();
            if (classStudentsResult.isOk() && CollUtil.isNotEmpty(classStudentsResult.getData())) {
                studentVOList = classStudentsResult.getData();
            }

            // 5. 如果是补课类型，需要获取原课次的临时学员（先添加到学生列表中）
            List<StudentVO> temporaryStudents = new ArrayList<>();
            if (Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_3.code)) {
                log.info("检测到补课类型课程，开始查询原课次的临时学员, lessonNo: {}", lessonNoLong);
                temporaryStudents = getTemporaryStudentsFromOriginalLesson(timetable, storeIdArg);
                if (CollUtil.isNotEmpty(temporaryStudents)) {
                    log.info("找到原课次临时学员数量: {}", temporaryStudents.size());

                    // 将临时学员添加到学生列表中（用于后续处理）
                    studentVOList.addAll(temporaryStudents);
                    // 去重，避免重复添加同一学员
                    studentVOList = new ArrayList<>(studentVOList.stream()
                        .collect(Collectors.toMap(StudentVO::getUserId, Function.identity(),
                            (existing, replacement) -> existing))
                        .values());
                    log.info("合并临时学员后的学生总数: {}", studentVOList.size());
                }
            }

            // 5. 处理班级学生与已有学生的差异
            Map<Long, BClassTimeStudent> existingStudentMap =
                existingStudents.stream()
                    .collect(
                        Collectors.toMap(
                            BClassTimeStudent::getStudentId, Function.identity(), (a, b) -> a));

            // 6. 如果是补课类型，创建补课临加学员记录
            if (Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_3.code)
                && CollUtil.isNotEmpty(temporaryStudents)) {
                // 获取原课次编号，用于设置来源课次
                Long originalLessonNo = getOriginalLessonNo(timetable, storeIdArg);

                // 为补课临加学员创建专门的记录，设置来源课次
                List<BClassTimeStudent> makeupTemporaryStudents = createMakeupTemporaryStudents(
                    temporaryStudents, lessonNoLong, originalLessonNo, storeIdArg,
                    existingStudents);

                if (CollUtil.isNotEmpty(makeupTemporaryStudents)) {
                    studentsToAdd.addAll(makeupTemporaryStudents);
                    log.info("添加补课临加学员记录数量: {}", makeupTemporaryStudents.size());

                    // 将新创建的补课临加学员添加到existingStudentMap中，避免getStudentsToAdd重复处理
                    for (BClassTimeStudent student : makeupTemporaryStudents) {
                        existingStudentMap.put(student.getStudentId(), student);
                    }
                    log.info("已将补课临加学员添加到existingStudentMap，避免重复处理");
                }
            }

            // 查询该课节相关的所有调课记录
            List<TimetableChange> timetableChanges =
                timetableChangeMapper.selectList(
                    Wrappers.<TimetableChange>lambdaQuery()
                        .and(
                            wrapper ->
                                wrapper
                                    .eq(TimetableChange::getSourceLessonNo, lessonNoLong)
                                    .or()
                                    .eq(TimetableChange::getTargetLessonNo, lessonNoLong)));

            // 7. 处理班级学生（不包括补课临加学员，因为已经在上面处理过了）
            List<BClassTimeStudent> classStudentsToAdd =
                getStudentsToAdd(
                    studentVOList, existingStudentMap, lessonNoLong, timetableChanges, storeIdArg,
                    timetable.getClassEndDateTime());

            if (CollUtil.isNotEmpty(classStudentsToAdd)) {
                studentsToAdd.addAll(classStudentsToAdd);
                log.info("添加班级学生记录数量: {}", classStudentsToAdd.size());
            }

            // 找出需要标记删除的学生(班级学生类型且不在当前班级中)
            studentsToDelete =
                filterStudentsToDelete(lessonNoLong, existingStudents, studentVOList,
                    timetableChanges);

            // 更新课次学生正式/试听状态
            if (CollUtil.isNotEmpty(studentVOList)) {
                updateClassTimeStudentStatus(existingStudents, studentVOList);
            }
        } else {
            log.info("课程已结束，跳过学生状态对比和删除操作");

            // 新增逻辑：如果课程已结束，检查是否需要补充学生数据
            // 1. 完全没有学生记录的情况
            // 2. 有学生但可能缺失原班级学生的情况（例如只有调课学员）
            boolean needSupplementStudents = false;
            String supplementReason = "";

            if (CollUtil.isEmpty(existingStudents)) {
                needSupplementStudents = true;
                supplementReason = "课次下没有任何学生记录";
            } else {
                // 检查是否需要补充原班级学生
                if (needSupplementClassStudents(timetable, existingStudents)) {
                    needSupplementStudents = true;
                    supplementReason = "课次下可能缺失原班级学生";
                }
            }

            if (needSupplementStudents) {
                log.info("课程已结束且{}，开始插入班级学生到课次学生表, lessonNo: {}",
                    supplementReason, lessonNoLong);
                try {
                    saveClassStudentIntoClassTimeStudent(lessonNoLong);
                    log.info("成功插入班级学生到课次学生表, lessonNo: {}", lessonNoLong);

                    // 重新查询课次学生记录，以便后续逻辑能正常处理
                    existingStudents = this.list(
                        Wrappers.<BClassTimeStudent>lambdaQuery()
                            .eq(BClassTimeStudent::getLessonNo, lessonNoLong)
                            .eq(BClassTimeStudent::getStoreId, storeIdArg));
                    log.info("重新查询后的课次学生记录数: {}", existingStudents.size());
                } catch (Exception e) {
                    log.error("插入班级学生到课次学生表失败, lessonNo: {}", lessonNoLong, e);
                }
            }

            // 对于已结束的补课，也需要确保包含原课次的临时学员
            if (Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_3.code)) {
                log.info(
                    "检测到已结束的补课类型课程，检查是否需要补充原课次的临时学员, lessonNo: {}",
                    lessonNoLong);
                ensureTemporaryStudentsForEndedMakeupCourse(timetable, storeIdArg, lessonNoLong);
            }
        }

        // 6. 执行数据库操作
        if (CollUtil.isNotEmpty(studentsToAdd)) {
            log.info("批量新增班级学生, 数量: {}", studentsToAdd.size());
            this.saveBatch(studentsToAdd);
        }

        if (CollUtil.isNotEmpty(studentsToDelete)) {
            log.info("批量删除不在班级中的学生, 数量: {}", studentsToDelete.size());
            this.removeBatchByIds(studentsToDelete);
        }

        // 7. 重新查询最新的课次学生列表
        List<BClassTimeStudent> finalStudents =
            this.list(
                Wrappers.<BClassTimeStudent>lambdaQuery()
                    .ne(BClassTimeStudent::getAdjustStatus, YesNoEnum.YES.getCode())
                    .ne(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode())) // 过滤手动移除的学员
                    .eq(BClassTimeStudent::getLessonNo, lessonNoLong)
                    .eq(BClassTimeStudent::getStoreId, storeIdArg));
        log.info("最终课次学生记录数: {}", finalStudents.size());

        // 8. 组装返回数据
        CheckInStudentVO result = new CheckInStudentVO();
        result.setLessonName(lessonName);

        // 9. 获取学生详细信息
        List<Integer> studentIds =
            finalStudents.stream()
                .map(BClassTimeStudent::getStudentId)
                .map(e -> Integer.valueOf(e.toString()))
                .collect(Collectors.toList());
        List<StudentCheckInInfoVO> studentList = new ArrayList<>();

        remoteAndStudentInfo(studentIds, finalStudents, studentList);

        result.setStudents(studentList);
        result.setTotalCount(studentList.size());
        result.setClassStartDateTime(timetable.getClassStartDateTime());
        result.setClassEndDateTime(timetable.getClassEndDateTime());
        result.setCheckedInCount(
            (int)
                studentList.stream()
                    .filter(s -> s.getCheckInStatus() != null && s.getCheckInStatus() == 1)
                    .count());

        log.info(
            "获取课表签到考勤学生列表完成, 总人数: {}, 已签到: {}, 过滤掉的试听学员数: {}, 课程是否已结束: {}",
            result.getTotalCount(),
            result.getCheckedInCount(),
            finalStudents.size() - studentList.size(),
            isClassEnded);
        return result;
    }

    /**
     * 更新课次学生正式/试听状态
     *
     * @param finalStudents
     * @param studentVOList
     * @return void
     * <AUTHOR>
     * @date 2025/3/14 11:17
     */
    private void updateClassTimeStudentStatus(List<BClassTimeStudent> finalStudents,
        List<StudentVO> studentVOList) {

        List<BClassTimeStudent> classTimeStudentList = new ArrayList<>();
        for (BClassTimeStudent finalStudent : finalStudents) {

            StudentVO studentVO = studentVOList.stream()
                .filter(e -> e.getUserId().equals(finalStudent.getStudentId()))
                .findFirst()
                .orElse(new StudentVO());

            BClassTimeStudent bClassTimeStudent = new BClassTimeStudent();
            bClassTimeStudent.setId(finalStudent.getId());
            bClassTimeStudent.setIsRegularStudents(studentVO.getIsRegularStudents());
            classTimeStudentList.add(bClassTimeStudent);
        }
        this.updateBatchById(classTimeStudentList);

    }

    /**
     * 根据学生Id远程调用并返回对应的信息
     *
     * @param studentIds
     * @param finalStudents
     * @param studentList
     * @return void
     * <AUTHOR>
     * @date 2025/3/11 10:49
     */
    @Override
    public void remoteAndStudentInfo(
        List<Integer> studentIds,
        List<BClassTimeStudent> finalStudents,
        List<StudentCheckInInfoVO> studentList) {
        if (CollUtil.isNotEmpty(studentIds)) {
            R<List<StudentVO>> studentInfoResult = remoteStudentService.getStudentListByIds(
                studentIds.stream().map(Long::valueOf).collect(Collectors.toList()));
            log.info("批量查询学生信息结果: {}", JSON.toJSONString(studentInfoResult));
            Map<Long, StudentVO> studentInfoMap = new HashMap<>();
            if (studentInfoResult.isOk() && CollUtil.isNotEmpty(studentInfoResult.getData())) {
                studentInfoMap =
                    studentInfoResult.getData().stream()
                        .collect(Collectors.toMap(StudentVO::getUserId, Function.identity(),
                            (a, b) -> a));
            }
            // 2. 预先批量查询所有答题器信息
            Map<String, Integer> clickerSerialNumberMap = new HashMap<>();
            List<BClassTimeStudent> studentsWithClicker =
                finalStudents.stream()
                    .filter(
                        student ->
                            StringUtils.isNotBlank(student.getReceiverSnNumber())
                                && StringUtils.isNotBlank(student.getClickerSnNumber())
                                && !CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(
                                student.getReceiverSnNumber())
                                && !CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(
                                student.getClickerSnNumber()))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(studentsWithClicker)) {
                log.info("开始批量查询答题器序列号, 需要查询的学生数: {}",
                    studentsWithClicker.size());
                List<String> receiverSnNumbers =
                    studentsWithClicker.stream()
                        .map(BClassTimeStudent::getReceiverSnNumber)
                        .collect(Collectors.toList());
                List<String> clickerSnNumbers =
                    studentsWithClicker.stream()
                        .map(BClassTimeStudent::getClickerSnNumber)
                        .collect(Collectors.toList());

                log.info("查询答题器序列号 - 接收器SN列表：{}，答题器SN列表：{}",
                    receiverSnNumbers, clickerSnNumbers);

                List<BInteractionReceiverClicker> serialNumbers =
                    bInteractionReceiverClickerService.getClickerSerialNumbers(receiverSnNumbers,
                        clickerSnNumbers);
                if (CollUtil.isNotEmpty(serialNumbers)) {
                    serialNumbers.forEach(
                        clicker -> {
                            String key =
                                clicker.getReceiverSnNumber() + "_" + clicker.getSnNumber();
                            clickerSerialNumberMap.put(key, clicker.getSerialNumber());
                            log.debug("添加答题器序列号映射 - key：{}，序列号：{}", key,
                                clicker.getSerialNumber());
                        });
                }
                log.info("批量查询答题器序列号完成, 查询到的记录数: {}，映射关系：{}",
                    clickerSerialNumberMap.size(), clickerSerialNumberMap);
            }
            List<Long> trialIds = Lists.newArrayList();
            for (BClassTimeStudent student : finalStudents) {
                // 获取学生详细信息
                StudentVO studentVO = studentInfoMap.get(student.getStudentId());
                if (studentVO == null) {
                    log.warn("未找到学生详细信息, studentId: {}", student.getStudentId());
                    continue;
                }

                // 判断试听学员课次数如果该学生在这节课次中已经签到过则直接返回 SP23 试听学员为0正常也返回
//                if (StudentStatusEnum.TRIAL.getCode() == studentVO.getStatus()
//                    && !Objects.equals(
//                    student.getCheckInStatus(), CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
//                    Integer remainingLessons = studentVO.getCourseHours();
//                    if (remainingLessons == null || remainingLessons <= 0) {
//                        log.info("试听学员[{}]剩余课次数为{}，不返回该学生信息", studentVO.getName(),
//                            remainingLessons);
//                        trialIds.add(student.getId());
//                        continue;
//                    }
//                }

                StudentCheckInInfoVO studentInfo = new StudentCheckInInfoVO();
                studentInfo.setClassStudentId(student.getId());
                studentInfo.setStudentId(student.getStudentId());
                studentInfo.setStudentType(student.getStudentType());
                studentInfo.setCheckInStatus(student.getCheckInStatus());
                // 设置是否为临加学员
                studentInfo.setIsTemporary(
                    Objects.equals(student.getStudentType(), StudentTypeEnum.STUDENT_TYPE_4.code));

                // 填充学生详细信息
                studentInfo.setStudentName(studentVO.getName());
                studentInfo.setStudentMobile(studentVO.getPhone());
                studentInfo.setGender(studentVO.getSex());
                studentInfo.setRemainingLessons(studentVO.getCourseHours());
                studentInfo.setStageName(studentVO.getStageName());
                studentInfo.setStageId(studentVO.getStageId());
                studentInfo.setStatus(studentVO.getStatus());
                studentInfo.setLessonNo(student.getLessonNo());
                studentInfo.setCheckInTime(student.getCheckInTime());
                // 处理答题器绑定状态
                String bindClickerStatus = "未绑定答题器";
                Integer bindClickerStatusType = ClickerBindStatusEnum.STATUS_ENUM_0.code;
                if (StringUtils.isNotBlank(student.getReceiverSnNumber())
                    && StringUtils.isNotBlank(student.getClickerSnNumber())) {
                    if (CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(
                        student.getReceiverSnNumber())
                        && CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(
                        student.getClickerSnNumber())) {
                        bindClickerStatus = "答题器绑定中...";
                        bindClickerStatusType = ClickerBindStatusEnum.STATUS_ENUM_1.code;
                    } else {
                        // 从预先查询的Map中获取序列号
                        String key =
                            student.getReceiverSnNumber() + "_" + student.getClickerSnNumber();
                        Integer serialNumber = clickerSerialNumberMap.get(key);
                        if (Objects.nonNull(serialNumber)) {
                            String formattedSerialNumber = String.format("%02d", serialNumber);
                            bindClickerStatus = "答题器编号：" + formattedSerialNumber;
                            bindClickerStatusType = ClickerBindStatusEnum.STATUS_ENUM_2.code;
                        }
                    }
                }
                studentInfo.setBindClickerStatus(bindClickerStatus);
                studentInfo.setBindClickerStatusType(bindClickerStatusType);
                studentInfo.setPinyinPre(studentVO.getPinyinPre());

                studentList.add(studentInfo);
            }
            studentList.sort(
                Comparator.nullsLast(Comparator.comparing(StudentCheckInInfoVO::getPinyinPre,
                        Comparator.nullsLast(PinyinComparatorUtil.getInstance())))
                    .thenComparing(StudentCheckInInfoVO::getStudentId));
            if (CollUtil.isNotEmpty(trialIds)) {
                this.removeBatchByIds(trialIds);
            }
        }
    }

    /**
     * 对比那些学生需要新增加
     *
     * @param classStudent
     * @param existingStudentMap
     * @param lessonNoLong
     * @param timetableChanges
     * @param storeId
     * @param courseEndTime      课程结束时间
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent>
     * <AUTHOR>
     * @date 2025/3/4 18:15
     */
    private List<BClassTimeStudent> getStudentsToAdd(
        List<StudentVO> classStudent,
        Map<Long, BClassTimeStudent> existingStudentMap,
        Long lessonNoLong,
        List<TimetableChange> timetableChanges,
        Long storeId,
        LocalDateTime courseEndTime) {

        log.info("开始计算需要新增的学生, 班级学生数: {}, 已有学生数: {}", classStudent.size(),
            existingStudentMap.size());
        classStudent =
            classStudent.stream()
                .filter(student -> !existingStudentMap.containsKey(student.getUserId()))
                .toList();
        List<BClassTimeStudent> studentsToAdd = new ArrayList<>();

        log.info(
            "查询到调课记录数: {}, 记录详情: {}", timetableChanges.size(),
            JSON.toJSONString(timetableChanges));

        Map<Long, Boolean> studentValidityMap = analyzeTimetableChanges(timetableChanges,
            lessonNoLong);

        for (StudentVO student : classStudent) {
            BClassTimeStudent classTimeStudent = existingStudentMap.get(student.getUserId());
            boolean isStatus =
                !Objects.equals(student.getClassStudentStatus(), ClassStudentEnum.FORMAL.getCode())
                    || Objects.equals(student.getStatus(), StudentStatusEnum.LEAVE.getCode())
                    || (Objects.nonNull(classTimeStudent)
                    && Objects.equals(
                    classTimeStudent.getAdjustStatus(),
                    Integer.parseInt(YesNoEnum.YES.getCode())));
            // 检查调课有效性
            Boolean isValid = studentValidityMap.get(student.getUserId());
            log.info("学生[{}]在调课链中的有效性: {}", student.getUserId(), isValid);

            if (!isStatus && (Objects.isNull(isValid) || isValid)) {
                // 检查学生在课程中的有效性（包括时间过滤）
                boolean isStudentValid = isStudentValidForEndedCourse(student, courseEndTime,
                    false);

                if (isStudentValid) {
                    // 检查是否已存在记录
                    BClassTimeStudent existingStudent = existingStudentMap.get(student.getUserId());
                    if (existingStudent == null) {
                        log.info("发现需要新增的在班学生: studentId={}", student.getUserId());
                        BClassTimeStudent newStudent = new BClassTimeStudent();
                        newStudent.setStoreId(storeId);
                        newStudent.setStudentId(student.getUserId());
                        newStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_1.code);
                        newStudent.setLessonNo(lessonNoLong);
                        newStudent.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_0.code);
                        studentsToAdd.add(newStudent);
                    }
                } else {
                    log.debug("学生 {} 不满足时间过滤条件，不添加到课次学生表", student.getUserId());
                }
            }
        }

        log.info("计算完成,需要新增的学生数量: {}", studentsToAdd.size());
        return studentsToAdd;
    }

    /**
     * 筛选需要删除的学生ID列表
     *
     * @param existingStudents 现有的学生列表
     * @param studentVOList    当前班级学生ID列表
     * @return 需要删除的学生ID列表
     */
    private List<Long> filterStudentsToDelete(
        Long lessonNoLong,
        List<BClassTimeStudent> existingStudents,
        List<StudentVO> studentVOList,
        List<TimetableChange> timetableChanges) {

        log.info(
            "开始筛选需要删除的学生记录, 现有学生数: {}, 当前学生列表数: {}",
            existingStudents.size(), studentVOList.size());

        // 构建学生状态Map
        Map<Long, StudentVO> studentStatusMap =
            studentVOList.stream()
                .collect(Collectors.toMap(StudentVO::getUserId, Function.identity(), (a, b) -> a));

        Map<Long, Boolean> studentValidityMap = analyzeTimetableChanges(timetableChanges,
            lessonNoLong);

        List<Long> studentsToDelete =
            existingStudents.stream()
                .filter(
                    student -> {
                        log.info("开始检查学生: {}", student.getStudentId());

                        // 检查签到状态
                        if (Objects.equals(
                            student.getCheckInStatus(), CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
                            log.info("学生[{}]已签到,保留记录", student.getStudentId());
                            return false;
                        }

                        // 检查是否为临加学生
                        if (Objects.equals(
                            student.getStudentType(), StudentTypeEnum.STUDENT_TYPE_4.code)) {
                            log.info("学生[{}]为临加学生,保留记录", student.getStudentId());
                            return false;
                        }

                        // 检查调课有效性
                        Boolean isValid = studentValidityMap.get(student.getStudentId());
                        log.info("学生[{}]在调课链中的有效性: {}", student.getStudentId(), isValid);

                        if (isValid != null && isValid) {
                            return false;
                        }

                        // 获取学生状态信息
                        StudentVO studentVO = studentStatusMap.get(student.getStudentId());
                        log.info("学生[{}]的状态信息: {}", student.getStudentId(),
                            JSON.toJSONString(studentVO));

                        if (studentVO == null) {
                            log.warn("未找到学生[{}]的状态信息,暂不删除", student.getStudentId());
                            return false;
                        }

                        // 未签到且状态为退费、转班的需要删除
                        boolean needDelete =
                            !Objects.equals(
                                studentVO.getClassStudentStatus(),
                                ClassStudentEnum.FORMAL.getCode())
                                || Objects.equals(
                                studentVO.getStatus(), StudentStatusEnum.LEAVE.getCode())
                                || Objects.equals(
                                student.getAdjustStatus(),
                                Integer.parseInt(YesNoEnum.YES.getCode()));
                        if (needDelete) {
                            log.info("学生未签到且状态异常,将删除记录: studentId={}",
                                student.getStudentId());
                            return true;
                        }

                        return false;
                    })
                .map(BClassTimeStudent::getId)
                .collect(Collectors.toList());

        log.info("筛选完成,需要删除的学生: {}", studentsToDelete);
        return studentsToDelete;
    }

    /**
     * 检查课程时间是否允许操作 只有在课前30分钟内或课程进行中才允许操作
     *
     * @param timetable 课表信息
     * @throws RuntimeException 如果时间检查不通过
     */
    private void checkTimetableTimeForOperation(Timetable timetable, boolean isReissue) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime classStartDateTime = timetable.getClassStartDateTime();
        LocalDateTime classEndDateTime = timetable.getClassEndDateTime();

        if (classStartDateTime == null || classEndDateTime == null) {
            log.error("课程开始或结束时间为空, timetableId: {}", timetable.getId());
            throw new BizException("课程时间信息不完整");
        }

        log.info("当前时间: {}, 课程开始时间: {}, 课程结束时间: {}", now, classStartDateTime,
            classEndDateTime);

        //    if (isReissue) {
        //      // 补签检查：只能在课程结束后操作
        //      if (now.isBefore(classEndDateTime)) {
        //        log.error("课程未结束，不允许补签操作, timetableId: {}", timetable.getId());
        //        throw new BizException("课程未结束，不允许补签操作！");
        //      }
        //      log.info("补签时间检查通过，允许操作, timetableId: {}", timetable.getId());
        //      return;
        //    }

        // 签到检查：只能在课程开始前30分钟到课程结束前操作
        // 计算当前时间距离课程开始时间的分钟数
        long minutesBeforeStart =
            java.time.temporal.ChronoUnit.MINUTES.between(now, classStartDateTime);
        log.info("距离课程开始还有{}分钟", minutesBeforeStart);

        // 课程已结束
        if (!isReissue && now.isAfter(classEndDateTime)) {
            log.error("课程已结束，不允许签到操作, timetableId: {}", timetable.getId());
            throw new BizException("课程已结束，不允许签到操作！");
        }

        // 如果当前时间在课程开始前超过30分钟，则不允许操作
        if (!isReissue && now.isBefore(classStartDateTime) && minutesBeforeStart > 30) {
            log.error(
                "距离课程开始时间超过30分钟，不允许签到操作, timetableId: {}, minutesBeforeStart: {}",
                timetable.getId(),
                minutesBeforeStart);
            throw new BizException("距离课程开始时间超过30分钟，不允许签到操作！");
        }

        log.info("签到时间检查通过，允许操作, timetableId: {}", timetable.getId());
    }

    /**
     * 签到考勤
     *
     * @param bClassTimeStudentDTO
     * @return void
     * <AUTHOR>
     * @date 2025/2/26 9:46
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ClassStudentErrorVO> checkIn(BClassTimeStudentDTO bClassTimeStudentDTO) {
        return handleCheckIn(bClassTimeStudentDTO, false, true);
    }


    /**
     * 处理取消考勤
     */
    @Lock4j(keys = {"#bClassTimeStudentDTO.lessonNo", "#bClassTimeStudentDTO.studentId"})
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<ClassStudentErrorVO> cancelCheckIn(BClassTimeStudentDTO bClassTimeStudentDTO) {
        log.info("开始执行取消考勤, 参数: {}", bClassTimeStudentDTO);
        Long lessonNo = bClassTimeStudentDTO.getLessonNo();
        Long studentId = bClassTimeStudentDTO.getStudentId();
        Long storeId = StoreContextHolder.getStoreId();

        // 1. 查询课表信息
        Timetable timetable = getTimetableInfo(lessonNo, storeId);
        LocalDate classDate = timetable.getClassDate();
        //2 考勤结算周期是否锁定，锁定不允许取消考勤
        if (canCheckIn(classDate)) {
            log.info("考勤结算周期已锁定不允许取消考勤, lessonNo: {},studentId:{}",
                lessonNo,
                studentId);
            return R.ok(new ClassStudentErrorVO(
                ClassStudentErrorEnum.CHECKIN_SETTLEMENT_CYCLE_LOCKED_ERROR));
        }
        //2.2 获取学生信息,已退费学院不允许取消考勤
        StudentVO studentVO = getStudentInfo(studentId);
        if (studentVO.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            log.info("学员已退费，不允许取消考勤, lessonNo: {},studentId:{}", lessonNo, studentId);
            return R.ok(new ClassStudentErrorVO(
                ClassStudentErrorEnum.REFUND_STUDENT_CANCEL_CHECK_IN_ERROR));
        }
        //2.3 获取学生课次记录，当前课次学生是否已签到
        BClassTimeStudent classTimeStudent = getClassTimeStudent(lessonNo, studentId, storeId);
        if (!CheckInStatusEnum.CHECK_IN_STATUS_1.code.equals(classTimeStudent.getCheckInStatus())) {
            log.warn("学员未签到，不允许取消考勤, lessonNo: {},studentId:{}", lessonNo, studentId);
            return R.ok(
                new ClassStudentErrorVO(ClassStudentErrorEnum.NO_CHECK_IN_CANCEL_CHECK_IN_ERROR));
        }
        //3.更新课次学生表考勤状态为未签到
        boolean updated = updateToUnCheckIn(classTimeStudent);
        //取消考勤失败，可能被其他操作执行
        if (BooleanUtils.isFalse(updated)) {
            log.info("取消考勤失败，可能被其他操作执行, lessonNo: {},studentId:{}", lessonNo,
                studentId);
            return R.ok();
        }
        //4.归还课时
        returnCourseHours(studentId, timetable.getId());
        //4.课前和课中取消考勤，解绑答题器
//        if (!Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_4.code)) {
//            if (LocalDateTime.now().isBefore(timetable.getClassEndDateTime())) {
//                bInteractionReceiverService.unbindClicker(timetable, studentId);
//            }
//        }
        // 取消考勤假设已排线上补课则添加到线上补课
        handleMakeUpOnline(lessonNo, studentId, storeId, timetable, classTimeStudent);
        log.info("取消考勤操作完成, 学生: {}, 课次: {}", studentVO.getName(), lessonNo);
        return R.ok(new ClassStudentErrorVO());
    }

    /**
     * 添加线上补课
     *
     * @param lessonNo
     * @param studentId
     * @param storeId
     * @return void
     * <AUTHOR>
     * @date 2025/4/27 11:09
     */
    private void handleMakeUpOnline(
        Long lessonNo,
        Long studentId,
        Long storeId,
        Timetable timetable,
        BClassTimeStudent classTimeStudent) {
        log.info("开始处理线上补课, lessonNo: {}, studentId: {}, storeId: {}", lessonNo, studentId,
            storeId);

        if (!String.valueOf(lessonNo)
            .startsWith(String.valueOf(CourseTypeEnum.COURSE_TYPE_ENUM_4.code))) {
            log.info("普通课程取消考勤，跳过处理, lessonNo: {},studentId: {}", lessonNo, studentId);
        }
        // 线上补课取消考勤推送约读会员站
        else {
            log.info("线上补课取消考勤，准备推送到约读会员站, lessonNo: {}", lessonNo);

            String idStr = String.valueOf(lessonNo);
            String realIdStr = idStr.substring(idStr.length() - 15);
            log.info("解析线上补课ID: {}, 原始lessonNo: {}", realIdStr, lessonNo);

            BCourseMakeUpOnline makeUpOnline =
                bCourseMakeUpOnlineMapper.selectById(Long.valueOf(realIdStr));

            if (makeUpOnline == null) {
                log.warn("未找到线上补课记录, ID: {}", realIdStr);
                return;
            }

            log.info("找到线上补课记录: {}, 准备推送取消考勤信息到会员站", makeUpOnline.getId());
            courseMakeUpOnlineManager.cancelAttendanceToMemberSite(
                classTimeStudent, makeUpOnline, timetable);
            log.info(
                "完成推送取消考勤信息到会员站, 学生ID: {}, 补课ID: {}",
                classTimeStudent.getStudentId(),
                makeUpOnline.getId());
        }
        log.info("线上补课处理完成, lessonNo: {}, studentId: {}", lessonNo, studentId);
    }

    /**
     * 补签考勤
     *
     * @param bClassTimeStudentDTO
     * @return void
     * <AUTHOR>
     * @date 2025/2/26 10:57
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ClassStudentErrorVO> chckinReissue(
        BClassTimeStudentDTO bClassTimeStudentDTO, boolean showAlreadyCheckedInTip) {
        // 补签考勤校验该学员是否已经退费
        StudentVO studentVO = getStudentInfo(bClassTimeStudentDTO.getStudentId());
        if (studentVO.getStatus() == StudentStatusEnum.LEAVE.getCode()) {
            log.info("学员已退费，不允许补签考勤, lessonNo: {},studentId:{}",
                bClassTimeStudentDTO.getLessonNo(), bClassTimeStudentDTO.getStudentId());
            return R.ok(new ClassStudentErrorVO(
                ClassStudentErrorEnum.REFUND_STUDENT_CHECKIN_REISSUE_ERROR));
        }
        return handleCheckIn(bClassTimeStudentDTO, true, showAlreadyCheckedInTip);
    }

    /**
     * 添加补签考勤
     *
     * @param bClassTimeStudentDTO
     * @return void
     * <AUTHOR>
     * @date 2025/2/26 20:03
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ClassStudentErrorVO> addChckinReissue(BClassTimeStudentDTO bClassTimeStudentDTO) {
        log.info("开始执行批量添加补签考勤, 参数: {}", JSON.toJSONString(bClassTimeStudentDTO));

        Long lessonNo = bClassTimeStudentDTO.getLessonNo();
        List<Long> studentIds = bClassTimeStudentDTO.getStudentIds();
        Long storeId = StoreContextHolder.getStoreId();

        // 检查这些学生在该课次中的状态
        List<BClassTimeStudent> existingStudents =
            this.list(
                Wrappers.<BClassTimeStudent>lambdaQuery()
                    .eq(BClassTimeStudent::getLessonNo, lessonNo)
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .in(BClassTimeStudent::getStudentId, studentIds));

        // 将已存在的学生按签到状态分类
        Map<Long, BClassTimeStudent> existingStudentMap =
            existingStudents.stream()
                .collect(Collectors.toMap(BClassTimeStudent::getStudentId, Function.identity()));

        // 区分需要处理的学生
        List<Long> studentsToAdd = new ArrayList<>(); // 需要新增的学生
        List<Long> studentsToUpdate = new ArrayList<>(); // 需要更新签到状态的学生
        List<Long> studentsToSkip = new ArrayList<>(); // 已签到无需处理的学生

        for (Long studentId : studentIds) {
            BClassTimeStudent existingStudent = existingStudentMap.get(studentId);
            if (existingStudent == null) {
                // 不存在的学生需要新增
                studentsToAdd.add(studentId);
            } else if (existingStudent.getCheckInStatus() == null
                || existingStudent.getCheckInStatus()
                .equals(CheckInStatusEnum.CHECK_IN_STATUS_0.code)) {
                // 未签到的学生需要更新状态
                studentsToUpdate.add(studentId);
            } else {
                // 已签到的学生跳过处理
                studentsToSkip.add(studentId);
            }
        }

        log.info(
            "学生分类处理结果 - 需要新增: {}, 需要更新签到: {}, 已签到跳过: {}",
            studentsToAdd.size(),
            studentsToUpdate.size(),
            studentsToSkip.size());

        try {
            // 1. 处理需要新增的学生
            if (!studentsToAdd.isEmpty()) {

                // 根据id列表获取学生列表
                List<Integer> integerStudentIdList = studentsToAdd.stream()
                    .map(Long::intValue)
                    .collect(Collectors.toList());
                R<List<StudentVO>> studentInfoResult = remoteStudentService.getStudentListByIds(
                    integerStudentIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
                log.info("批量查询学生信息结果: {}", JSON.toJSONString(studentInfoResult));
                Map<Long, StudentVO> studentInfoMap = new HashMap<>();
                if (studentInfoResult.isOk() && CollectionUtils.isNotEmpty(
                    studentInfoResult.getData())) {
                    studentInfoMap =
                        studentInfoResult.getData().stream()
                            .collect(Collectors.toMap(StudentVO::getUserId, Function.identity(),
                                (a, b) -> a));
                }

                List<BClassTimeStudent> newStudents = new ArrayList<>();
                for (Long studentId : studentsToAdd) {
                    BClassTimeStudent newStudent = new BClassTimeStudent();
                    newStudent.setStoreId(storeId);
                    newStudent.setStudentId(studentId);
                    newStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_3.code); // 3-添加考勤学生
                    newStudent.setLessonNo(lessonNo);
                    newStudent.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 默认未考勤

                    // 学生试听/正式状态
                    StudentVO studentVO = studentInfoMap.get(studentId);
                    if (Objects.nonNull(studentVO)) {
                        newStudent.setIsRegularStudents(studentVO.getIsRegularStudents());
                    }
                    newStudents.add(newStudent);
                }

                boolean saveResult = this.saveBatch(newStudents);
                if (!saveResult) {
                    log.error("批量保存新增补签学生记录失败");
                    throw new BizException("保存新增补签学生记录失败");
                }
                log.info("成功批量保存新增补签学生记录, 数量: {}", newStudents.size());
            }

            // 2. 合并需要进行补签操作的学生列表(新增的+需要更新的)
            List<Long> studentsToCheckIn = new ArrayList<>();
            studentsToCheckIn.addAll(studentsToAdd);
            studentsToCheckIn.addAll(studentsToUpdate);

            // 3. 对需要补签的学生执行补签操作
            for (Long studentId : studentsToCheckIn) {
                try {
                    BClassTimeStudentDTO checkInDTO = new BClassTimeStudentDTO();
                    checkInDTO.setLessonNo(lessonNo);
                    checkInDTO.setStudentId(studentId);
                    checkInDTO.setStoreId(storeId);
                    checkInDTO.setCourseTypeId(bClassTimeStudentDTO.getCourseTypeId());
                    checkInDTO.setForceCheckIn(bClassTimeStudentDTO.getForceCheckIn());

                    R<ClassStudentErrorVO> checkInResult = chckinReissue(checkInDTO, false);
                    log.info("学生补签结果: {}, studentId: {}", JSON.toJSONString(checkInResult),
                        studentId);

                    // 判断补签结果
                    if (!checkInResult.isOk()
                        || (Objects.nonNull(checkInResult.getData()) && !Objects.equals(
                        checkInResult.getData().getCode(),
                        Integer.valueOf(YesNoEnum.NO.getCode())))) {
                        // 记录详细的错误信息
                        log.warn(
                            "学生补签失败 - studentId: {}, lessonNo: {}, storeId: {}, 错误信息: {}, 错误码: {}",
                            studentId,
                            lessonNo,
                            storeId,
                            checkInResult.getMsg(),
                            Objects.nonNull(checkInResult.getData()) ? checkInResult.getData()
                                .getCode() : "null");

                        // 补签失败时,如果是新增的学生则删除记录
                        if (studentsToAdd.contains(studentId)) {
                            log.info(
                                "删除新增失败的补签学生记录 - studentId: {}, lessonNo: {}, storeId: {}",
                                studentId,
                                lessonNo,
                                storeId);

                            boolean removeResult = this.remove(
                                Wrappers.<BClassTimeStudent>lambdaQuery()
                                    .eq(BClassTimeStudent::getLessonNo, lessonNo)
                                    .eq(BClassTimeStudent::getStudentId, studentId)
                                    .eq(BClassTimeStudent::getStoreId, storeId));

                            if (!removeResult) {
                                log.warn(
                                    "删除新增失败的补签学生记录失败 - studentId: {}, lessonNo: {}, storeId: {}",
                                    studentId,
                                    lessonNo,
                                    storeId);
                            }
                        }
                        return checkInResult;
                    }

                    // 判断业务错误码
                    ClassStudentErrorVO errorVO = checkInResult.getData();
                    if (errorVO != null && errorVO.getCode() != null && errorVO.getCode() != 0) {
                        log.error(
                            "学生补签业务异常, studentId: {}, 错误码: {}, 错误信息: {}",
                            studentId,
                            errorVO.getCode(),
                            errorVO.getMsg());
                        // 补签失败时,如果是新增的学生则删除记录
                        if (studentsToAdd.contains(studentId)) {
                            this.remove(
                                Wrappers.<BClassTimeStudent>lambdaQuery()
                                    .eq(BClassTimeStudent::getLessonNo, lessonNo)
                                    .eq(BClassTimeStudent::getStudentId, studentId)
                                    .eq(BClassTimeStudent::getStoreId, storeId));
                        }
                        return checkInResult;
                    }

                    log.info("学生补签成功, studentId: {}", studentId);
                } catch (Exception e) {
                    log.error("学生补签失败, studentId: {}, 错误: {}", studentId, e.getMessage());
                    throw new BizException("学生[" + studentId + "]补签失败: " + e.getMessage());
                }
            }

            if (!studentsToSkip.isEmpty()) {
                log.info("以下学生已完成签到,跳过处理: {}", studentsToSkip);
            }

            log.info(
                "批量添加补签考勤完成, 总数: {}, 成功处理: {}, 跳过已签到: {}",
                studentIds.size(),
                studentsToCheckIn.size(),
                studentsToSkip.size());
        } catch (Exception e) {
            log.error("批量添加补签考勤失败: {}", e.getMessage(), e);
            throw new BizException("批量添加补签考勤失败: " + e.getMessage());
        }
        return R.ok(new ClassStudentErrorVO());
    }

    @Override
    public List<BClassTimeStudentVO> getBClassTimeStudentList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return list(
            Wrappers.<BClassTimeStudent>lambdaQuery()
                .in(BClassTimeStudent::getLessonNo, ids)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode()))) // 过滤手动移除的学员
            .stream().map(s -> {
                BClassTimeStudentVO bClassTimeStudentVO = new BClassTimeStudentVO();
                BeanUtils.copyProperties(s, bClassTimeStudentVO);
                return bClassTimeStudentVO;
            }).toList();
    }

    /**
     * 处理考勤的公共方法
     *
     * @param bClassTimeStudentDTO 考勤参数
     * @param isReissue            是否为补签
     * @return void
     */
    private R<ClassStudentErrorVO> handleCheckIn(
        BClassTimeStudentDTO bClassTimeStudentDTO,
        boolean isReissue,
        boolean showAlreadyCheckedInTip) {
        log.info(
            "开始执行{}考勤操作, 参数: {}", isReissue ? "补签" : "签到",
            JSON.toJSONString(bClassTimeStudentDTO));
        Long lessonNo = bClassTimeStudentDTO.getLessonNo();
        Long studentId = bClassTimeStudentDTO.getStudentId();
        Long storeId = StoreContextHolder.getStoreId();

        // 校验是否重复签到
        checkRepeatCheckIn(lessonNo, studentId, storeId);

        // 1. 查询课表信息
        Timetable timetable = getTimetableInfo(lessonNo, storeId);

        //签到、补签考勤、取消考勤、添加考勤需要校验结算周期是否锁定
        if (canCheckIn(timetable.getClassDate())) {
            return R.ok(new ClassStudentErrorVO(
                ClassStudentErrorEnum.CHECKIN_SETTLEMENT_CYCLE_LOCKED_ERROR));
        }

        // 2. 补签与正常签到的时间校验
        if (!Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_4.code)) {
            checkTimetableTimeForOperation(timetable, isReissue);
        }

        // 3. 获取学生信息
        StudentVO studentVO = getStudentInfo(studentId);

        // 4. 获取学生课次记录
        BClassTimeStudent classTimeStudent = getClassTimeStudent(lessonNo, studentId, storeId, isReissue);

        // 4.1 检查学生是否已在线上补课中签到考勤
        if (!isReissue) { // 补签操作不检查线上补课状态
            boolean hasOnlineMakeUpCheckedIn = checkStudentOnlineMakeUpCheckedIn(studentId, lessonNo, storeId);
            if (hasOnlineMakeUpCheckedIn) {
                log.info("学生已在线上补课中签到，跳过考勤操作但允许其他操作, studentId: {}, lessonNo: {}",
                    studentId, lessonNo);
                // 返回特定的提示信息，但不阻断后续的绑定答题器等操作
                ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
                errorVO.setCode(ClassStudentErrorEnum.ONLINE_MAKEUP_ALREADY_CHECKED_IN.getCode());
                errorVO.setMsg("该学员已排线上补课并已签到考勤！");
                return R.ok(errorVO);
            }
        }

        if (classTimeStudent.getCheckInStatus() != null
            && classTimeStudent.getCheckInStatus()
            .equals(CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
            log.warn("学生[{}]已完成考勤，无需{}操作", studentVO.getName(),
                isReissue ? "补签" : "签到");

            // 根据参数决定是否显示提示
            if (showAlreadyCheckedInTip) {
                throw new BizException(
                    String.format("学员[%s]已完成考勤，无需%s操作", studentVO.getName(),
                        isReissue ? "补签" : "签到"));
            } else {
                log.info("忽略已签到提示，直接返回成功");
                return R.ok(new ClassStudentErrorVO());
            }
        }

        // 5. 检查学生考勤状态(仅补签需要)
        if (isReissue) {
            // 检查学生是否从该课程调出，如果调出则不允许补签原课程
            if (classTimeStudentManager.isStudentTransferredFromLesson(studentId, lessonNo)) {
                log.error("学生已从该课程调出，不支持补签考勤, studentId: {}, lessonNo: {}",
                    studentId, lessonNo);
                return R.ok(new ClassStudentErrorVO(
                    ClassStudentErrorEnum.TRANSFERRED_STUDENT_REISSUE_ERROR));
            }
            checkStudentAlreadyCheckedIn(classTimeStudent, studentVO.getName());
        }

        // 6. 检查是否需要扣减课时
        boolean needDeductCourseHours =
            isReissue
                || classTimeStudent.getCheckInStatus() == null
                || classTimeStudent.getCheckInStatus()
                .equals(CheckInStatusEnum.CHECK_IN_STATUS_0.code);

        // 7. 检查学生剩余课时
        R<ClassStudentErrorVO> checkResult =
            checkRemainingLessons(
                studentVO, needDeductCourseHours, bClassTimeStudentDTO.getForceCheckIn());
        if (checkResult != null) {
            return checkResult;
        }

        // 8. 更新学生考勤状态
        updateStudentCheckInStatus(classTimeStudent, isReissue);

        // 9. 扣减课时(如需)
        if (needDeductCourseHours) {
            if (bClassTimeStudentDTO.getCourseTypeId() != null) {
                // 使用指定的课程类型进行扣减
                classTimeStudentManager.deductCourseHours(studentId, timetable.getId(),
                    bClassTimeStudentDTO.getCourseTypeId());
                log.info("使用指定课程类型扣减课时, courseType: {}",
                    bClassTimeStudentDTO.getCourseTypeId());
            } else {
                throw new BizException("操作失败！扣减课消必传课程类型！");
            }
        } else {
            log.info("学生已签到，无需扣减课时");
        }

        // 10. 扣减课时成功后异步更新学生阶段，阶段id取当前学生签到考勤课程所属的阶段，更新到学生的阶段信息里边
        CompletableFuture.runAsync(
            () -> {
                StudentUpdateDTO studentUpdateDTO = new StudentUpdateDTO();
                studentUpdateDTO.setUserId(studentId);
                // 从课表获取课程的阶段ID
                if (timetable.getCourseId() != null) {
                    // 通过课程ID获取阶段信息
                    CourseVO courseVO = getCourseInfo(timetable.getCourseId());
                    if (courseVO != null && courseVO.getStageId() != null) {
                        studentUpdateDTO.setStageId(courseVO.getStageId());
                        log.info("更新学生阶段信息, 学生ID: {}, 新阶段ID: {}", studentId,
                            courseVO.getStageId());
                        remoteStudentService.editStudentStageId(studentUpdateDTO);
                    } else {
                        log.warn("未找到课程阶段信息，无法更新学生阶段, 课程ID: {}",
                            timetable.getCourseId());
                    }
                } else {
                    log.warn("未找到课表信息，无法更新学生阶段, 课次号: {}", lessonNo);
                }
            });

        // 11. 更新课表上课状态
        timetableMapper.update(
            Wrappers.lambdaUpdate(Timetable.class)
                .eq(Timetable::getLessonNo, classTimeStudent.getLessonNo())
                .set(Timetable::getAttendClassType, YesNoEnum.YES.getCode()));
        // 12. 分配绑定答题器
        if (!Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_4.code)) {
            // 检查该学员在当前课次是否已经绑定了答题器
            boolean isClickerAlreadyBound = isStudentClickerAlreadyBound(classTimeStudent);

            if (isClickerAlreadyBound) {
                log.info("学员[{}]在课次[{}]已绑定答题器，跳过答题器分配逻辑",
                    studentVO.getName(), lessonNo);
            } else {
                log.info("学员[{}]在课次[{}]未绑定答题器，执行答题器分配逻辑",
                    studentVO.getName(), lessonNo);
                R<ClassStudentErrorVO> distributeResult =
                    bInteractionReceiverService.bindClicker(timetable, studentVO, classTimeStudent);
                if (Objects.nonNull(distributeResult)
                    && Objects.nonNull(distributeResult.getData())
                    && Objects.nonNull(distributeResult.getData().getCode())) {
                    return distributeResult;
                }
            }
        }

        // 13. 线上补课签到通知处理
        // 当学生在线上补课课次中完成签到操作时，如果该学生尚未收到过该补课课次的微信课消通知，则自动发送课消通知
        if (Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_4.code)
            && Objects.equals(classTimeStudent.getCheckInStatus(), CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
            handleOnlineMakeupCheckInNotification(lessonNo, studentId, storeId, timetable, studentVO);
        }

        // 签到考勤将对应的已排的补课中学员做移除的操作
        removeMakeUpOnline(lessonNo, studentId, storeId, timetable, classTimeStudent);
        log.info("{}考勤操作完成, 学生: {}, 课次: {}", isReissue ? "补签" : "签到",
            studentVO.getName(), lessonNo);
        return R.ok(new ClassStudentErrorVO());
    }

    /**
     * 检查学员是否已经绑定了答题器
     *
     * @param classTimeStudent 学员课次记录
     * @return true-已绑定答题器，false-未绑定答题器
     * <AUTHOR>
     * @date 2025/7/16
     */
    private boolean isStudentClickerAlreadyBound(BClassTimeStudent classTimeStudent) {
        // 检查接收器SN码和答题器SN码是否都不为空且不为"绑定中"状态
        String receiverSnNumber = classTimeStudent.getReceiverSnNumber();
        String clickerSnNumber = classTimeStudent.getClickerSnNumber();

        // 如果任一字段为空，则表示未绑定
        if (StringUtils.isBlank(receiverSnNumber) || StringUtils.isBlank(clickerSnNumber)) {
            return false;
        }

        // 如果任一字段为"绑定中"状态，则表示正在绑定但未完成
        if (CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(receiverSnNumber)
            || CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(clickerSnNumber)) {
            return false;
        }

        // 两个字段都不为空且都不是"绑定中"状态，表示已成功绑定
        return true;
    }

    /**
     * 签到考勤将对应的已排的补课中学员做移除的操作
     *
     * @param lessonNo
     * @param studentId
     * @param storeId
     * @return void
     * <AUTHOR>
     * @date 2025/4/27 10:57
     */
    private void removeMakeUpOnline(
        Long lessonNo, Long studentId, Long storeId, Timetable timetable,
        BClassTimeStudent student) {
        log.info(
            "开始移除学员的线上补课记录, lessonNo: {}, studentId: {}, storeId: {}", lessonNo,
            studentId, storeId);

        if (!String.valueOf(lessonNo)
            .startsWith(String.valueOf(CourseTypeEnum.COURSE_TYPE_ENUM_4.code))) {
            log.info("当前为普通课程，跳过处理线上补课操作, lessonNo: {}", lessonNo);
        } else {
            BCourseMakeUpOnline courseMakeUpOnline = getCourseMakeUpOnline(lessonNo);
            //  推送约读会员站补课
            courseMakeUpOnlineManager.pushVideoToMemberSite(
                student, courseMakeUpOnline, timetable);
            log.info("当前已是线上补课课程，跳过移除操作, lessonNo: {}", lessonNo);
        }
    }

    /**
     * 处理线上补课签到通知
     *
     * 当学生在线上补课课次中完成签到操作时，如果该学生尚未收到过该补课课次的微信课消通知，
     * 则自动发送课消通知。此功能确保学生能及时收到补课消费提醒。
     *
     * @param lessonNo 课次编号（线上补课格式）
     * @param studentId 学生ID
     * @param storeId 门店ID
     * @param timetable 课表信息
     * @param studentVO 学生信息
     * <AUTHOR>
     * @date 2025/07/15
     */
    private void handleOnlineMakeupCheckInNotification(Long lessonNo, Long studentId, Long storeId,
                                                      Timetable timetable, StudentVO studentVO) {
        try {
            log.info("开始处理线上补课签到通知, lessonNo: {}, studentId: {}, studentName: {}",
                lessonNo, studentId, studentVO.getName());

            // 1. 解析线上补课ID
            String lessonNoStr = String.valueOf(lessonNo);
            if (lessonNoStr.length() < 15) {
                log.warn("无效的线上补课课次编号, lessonNo: {}", lessonNo);
                return;
            }

            String makeupIdStr = lessonNoStr.substring(lessonNoStr.length() - 15);
            Long makeupId = Long.valueOf(makeupIdStr);
            log.info("解析得到线上补课ID: {}", makeupId);

            // 2. 查询补课信息
            BCourseMakeUpOnline makeupOnline = bCourseMakeUpOnlineMapper.selectById(makeupId);
            if (makeupOnline == null) {
                log.warn("未找到线上补课记录, makeupId: {}", makeupId);
                return;
            }

            // 3. 查询原始课次信息
            Timetable originalTimetable = timetableMapper.selectOne(
                Wrappers.<Timetable>lambdaQuery()
                    .eq(Timetable::getLessonNo, makeupOnline.getLessonNo())
                    .eq(Timetable::getStoreId, storeId)
            );

            if (originalTimetable == null) {
                log.warn("未找到原始课次信息, originalLessonNo: {}", makeupOnline.getLessonNo());
                return;
            }

            // 4. 构建学生签到信息
            StudentCheckInInfoVO studentCheckInInfo = new StudentCheckInInfoVO();
            studentCheckInInfo.setStudentId(studentId);
            studentCheckInInfo.setStudentName(studentVO.getName());

            List<StudentCheckInInfoVO> studentsToNotify = Collections.singletonList(studentCheckInInfo);

            // 5. 异步发送通知（不影响主流程性能）
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("异步发送线上补课签到通知, makeupId: {}, studentId: {}", makeupId, studentId);

                    R result = onlineMakeupNotificationService.sendNotificationsForAttendedStudents(
                        makeupOnline, originalTimetable, studentsToNotify);

                    if (result.isOk()) {
                        log.info("线上补课签到通知发送成功, makeupId: {}, studentId: {}", makeupId, studentId);
                    } else {
                        log.warn("线上补课签到通知发送失败, makeupId: {}, studentId: {}, result: {}",
                            makeupId, studentId, result);
                    }
                } catch (Exception e) {
                    log.error("异步发送线上补课签到通知异常, makeupId: {}, studentId: {}",
                        makeupId, studentId, e);
                }
            });

            log.info("线上补课签到通知处理完成, lessonNo: {}, studentId: {}", lessonNo, studentId);

        } catch (Exception e) {
            // 通知发送失败不应影响签到操作的成功
            log.error("处理线上补课签到通知异常, lessonNo: {}, studentId: {}, 但不影响签到流程",
                lessonNo, studentId, e);
        }
    }

    /**
     * 校验是否重复签到
     *
     * @param lessonNo
     * @param studentId
     * @param storeId
     * @return void
     * <AUTHOR>
     * @date 2025/4/27 10:42
     */
    private void checkRepeatCheckIn(Long lessonNo, Long studentId, Long storeId) {
        // 参数校验
        if (lessonNo == null || studentId == null || storeId == null) {
            throw new IllegalArgumentException("lessonNo、studentId和storeId不能为空");
        }

        log.info(
            "开始检查学员是否重复签到, lessonNo: {}, studentId: {}, storeId: {}", lessonNo,
            studentId, storeId);

        try {
            String lessonNoStr = String.valueOf(lessonNo);
            if (!lessonNoStr.startsWith(String.valueOf(CourseTypeEnum.COURSE_TYPE_ENUM_4.code))) {
                log.info("当前为普通课程，检查是否已在线上补课中签到, lessonNo: {}", lessonNo);
                getCheckInMakeUp(lessonNo, studentId, storeId);
                log.info("重复签到检查通过, 学生ID: {}, 课次: {}", studentId, lessonNo);
            } else {
                if (lessonNoStr.length() < 15) {
                    throw new BizException("无效的线上补课课次编号");
                }
                String realIdStr = lessonNoStr.substring(lessonNoStr.length() - 15);
                log.info("解析线上补课ID: {}, 原始lessonNo: {}", realIdStr, lessonNo);

                BCourseMakeUpOnline makeUpOnline = bCourseMakeUpOnlineMapper.selectById(
                    Long.valueOf(realIdStr));
                if (makeUpOnline == null) {
                    throw new BizException("未找到对应的线上补课记录");
                }
                // 校验是不是在原课次又签到了
                BClassTimeStudent classTimeStudent =
                    this.getOne(
                        Wrappers.<BClassTimeStudent>lambdaQuery()
                            .eq(BClassTimeStudent::getLessonNo, makeUpOnline.getLessonNo())
                            .eq(BClassTimeStudent::getStudentId, studentId)
                            .eq(BClassTimeStudent::getStoreId, storeId));
                if (classTimeStudent.getCheckInStatus() != null
                    && classTimeStudent.getCheckInStatus()
                    .equals(CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
                    throw new BizException("该学员在原课次上已签到考勤！");
                }
                // 校验是不是在其他补课中签到了
                getCheckInMakeUp(makeUpOnline.getLessonNo(), studentId, storeId);
                log.info("当前为线上补课课程，跳过重复签到检查, lessonNo: {}", lessonNo);
            }
        } catch (NumberFormatException e) {
            log.error("课次编号格式错误: {}", lessonNo, e);
            throw new BizException("无效的课次编号格式");
        } catch (Exception e) {
            log.error("检查重复签到时发生错误: lessonNo={}, studentId={}, storeId={}", lessonNo,
                studentId, storeId, e);
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 检查学生是否已在线上补课中签到考勤
     *
     * @param studentId 学生ID
     * @param lessonNo 课次编号
     * @param storeId 门店ID
     * @return true-已在线上补课中签到；false-未签到
     */
    private boolean checkStudentOnlineMakeUpCheckedIn(Long studentId, Long lessonNo, Long storeId) {
        try {
            log.info("开始检查学生线上补课签到状态, studentId: {}, lessonNo: {}, storeId: {}",
                studentId, lessonNo, storeId);

            // 1. 查询该课次的所有线上补课记录
            List<BCourseMakeUpOnline> makeUpOnlineList = bCourseMakeUpOnlineMapper.selectList(
                Wrappers.lambdaQuery(BCourseMakeUpOnline.class)
                    .eq(BCourseMakeUpOnline::getLessonNo, lessonNo)
                    .eq(BCourseMakeUpOnline::getStoreId, storeId)
            );

            if (CollectionUtils.isEmpty(makeUpOnlineList)) {
                log.info("该课次没有线上补课记录, lessonNo: {}", lessonNo);
                return false;
            }

            // 2. 检查学生是否在任何线上补课中已签到（已扣减课时）
            for (BCourseMakeUpOnline makeUpOnline : makeUpOnlineList) {
                // 构造线上补课的lessonNo
                String makeUpLessonNo = CourseTypeEnum.COURSE_TYPE_ENUM_4.code
                    + makeUpOnline.getLessonOrder().toString()
                    + makeUpOnline.getId().toString();

                // 查询学生在该线上补课中是否已签到
                long checkInCount = this.count(
                    Wrappers.lambdaQuery(BClassTimeStudent.class)
                        .eq(BClassTimeStudent::getStudentId, studentId)
                        .eq(BClassTimeStudent::getLessonNo, Long.valueOf(makeUpLessonNo))
                        .eq(BClassTimeStudent::getStoreId, storeId)
                        .eq(BClassTimeStudent::getCheckInStatus, CheckInStatusEnum.CHECK_IN_STATUS_1.code)
                );

                if (checkInCount > 0) {
                    log.info("发现学生已在线上补课中签到, studentId: {}, makeUpLessonNo: {}",
                        studentId, makeUpLessonNo);
                    return true;
                }
            }

            log.info("学生未在任何线上补课中签到, studentId: {}, lessonNo: {}", studentId, lessonNo);
            return false;

        } catch (Exception e) {
            log.error("检查学生线上补课签到状态时发生异常, studentId: {}, lessonNo: {}",
                studentId, lessonNo, e);
            // 异常情况下为了安全起见，返回false，允许正常考勤流程
            return false;
        }
    }

    private void getCheckInMakeUp(Long lessonNo, Long studentId, Long storeId) {
        if (lessonNo == null || studentId == null || storeId == null) {
            throw new IllegalArgumentException("lessonNo、studentId和storeId不能为空");
        }

        try {
            List<BCourseMakeUpOnline> courseMakeUpOnlines = bCourseMakeUpOnlineMapper.selectList(
                Wrappers.lambdaQuery(BCourseMakeUpOnline.class)
                    .eq(BCourseMakeUpOnline::getLessonNo, lessonNo));

            if (courseMakeUpOnlines == null) {
                log.warn("查询线上补课记录返回null, lessonNo: {}", lessonNo);
                courseMakeUpOnlines = Collections.emptyList();
            }

            log.info("查询到与当前课程关联的线上补课数量: {}", courseMakeUpOnlines.size());

            for (BCourseMakeUpOnline makeUpOnline : courseMakeUpOnlines) {
                if (makeUpOnline == null || makeUpOnline.getLessonOrder() == null
                    || makeUpOnline.getId() == null) {
                    log.warn("跳过无效的补课记录");
                    continue;
                }

                String makeUpOnlineLessonNo = CourseTypeEnum.COURSE_TYPE_ENUM_4.code
                    + makeUpOnline.getLessonOrder().toString()
                    + makeUpOnline.getId().toString();

                log.info(
                    "检查线上补课课次编号: {}, 补课ID: {}, 课次顺序: {}",
                    makeUpOnlineLessonNo,
                    makeUpOnline.getId(),
                    makeUpOnline.getLessonOrder());

                try {
                    long checkInNum = this.count(
                        Wrappers.lambdaQuery(BClassTimeStudent.class)
                            .eq(BClassTimeStudent::getStudentId, studentId)
                            .eq(BClassTimeStudent::getLessonNo, makeUpOnlineLessonNo)
                            .eq(BClassTimeStudent::getStoreId, storeId)
                            .eq(BClassTimeStudent::getCheckInStatus,
                                CheckInStatusEnum.CHECK_IN_STATUS_1.code));

                    log.info(
                        "线上补课中签到状态查询结果, 学生ID: {}, 线上补课lessonNo: {}, 是否已签到: {}",
                        studentId,
                        makeUpOnlineLessonNo,
                        checkInNum > 0);

                    if (checkInNum > 0) {
                        log.warn("检测到学员已在线上补课中签到, 学生ID: {}, 线上补课lessonNo: {}",
                            studentId, makeUpOnlineLessonNo);
                        throw new BizException("该学员已排线上补课并已签到考勤！");
                    }
                } catch (Exception e) {
                    log.error("查询签到状态时发生错误: lessonNo={}, studentId={}",
                        makeUpOnlineLessonNo, studentId, e);
                    throw new BizException("查询签到状态失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("处理补课签到检查时发生错误: lessonNo={}, studentId={}, storeId={}",
                lessonNo, studentId, storeId, e);
            throw new BizException("补课签到检查失败: " + e.getMessage());
        }
    }

    /**
     * 补签和取消考勤才需要校验结算周期是否锁定
     *
     * @param classDate 上课日期
     */
    private boolean canCheckIn(LocalDate classDate) {
        if (Objects.isNull(classDate)) {
            throw new BizException("课程日期不能为空");
        }
        SettlementCycleQuery settlementCycleQuery = new SettlementCycleQuery();
        try {
            R<List<SettlementCycleVO>> settlementCycleVOList = remoteSettlementCycleService.getAllList(
                settlementCycleQuery);
            if (settlementCycleVOList.isOk()) {
                Optional<SettlementCycleVO> settlementCycleVOOptional = settlementCycleVOList.getData()
                    .stream().filter(
                        settlementCycleVO -> (classDate.isEqual(settlementCycleVO.getBeginDate())
                            || classDate.isAfter(settlementCycleVO.getBeginDate()))
                            && (classDate.isEqual(settlementCycleVO.getEndDate())
                            || classDate.isBefore(settlementCycleVO.getEndDate()))).findAny();
                if (settlementCycleVOOptional.isPresent()) {
                    SettlementCycleVO settlementCycleVO = settlementCycleVOOptional.get();
                    if (settlementCycleVO.getCheckinLocked() == 1) {
                        log.info("结算周期已锁定, 不允许操作");
                        return true;
                    } else {
                        log.info("结算周期未锁定, 允许操作");
                        return false;
                    }
                } else {
                    log.info("未找到匹配的结算周期, 允许操作");
                    return false;
                }
            } else {
                log.error("获取结算周期失败, 错误信息: {}", settlementCycleVOList.getMsg());
                throw new BizException("获取结算周期失败");
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询结算周期失败, 错误信息: {}", e.getMessage());
            throw new BizException("<UNK>");
        }
    }

    /**
     * 获取课表信息
     */
    private Timetable getTimetableInfo(Long lessonNo, Long storeId) {
        log.info("开始查询课表信息, lessonNo: {}, storeId: {}", lessonNo, storeId);
        Timetable timetable = new Timetable();
        if (!String.valueOf(lessonNo)
            .startsWith(String.valueOf(CourseTypeEnum.COURSE_TYPE_ENUM_4.code))) {
            timetable =
                timetableMapper.selectOne(
                    Wrappers.<Timetable>lambdaQuery()
                        .eq(Timetable::getLessonNo, lessonNo)
                        .eq(Timetable::getStoreId, storeId));
        } else {
            BCourseMakeUpOnline makeUpOnline = getCourseMakeUpOnline(lessonNo);
            timetable.setCourseType(CourseTypeEnum.COURSE_TYPE_ENUM_4.code);
            timetable.setClassDate(LocalDate.from(makeUpOnline.getCreateTime()));
            timetable.setId(makeUpOnline.getId());
            timetable.setCourseId(makeUpOnline.getCourseId());
            timetable.setLessonOrder(makeUpOnline.getLessonOrder());
            timetable.setLectureId(makeUpOnline.getLectureId());
            timetable.setClassStartDateTime(
                LocalDateTime.of(makeUpOnline.getClassDate(), makeUpOnline.getClassStartTime()));
            timetable.setClassEndDateTime(
                LocalDateTime.of(makeUpOnline.getClassDate(), makeUpOnline.getClassEndTime()));
        }
        if (timetable == null) {
            log.error("未找到对应的课表信息, lessonNo: {}", lessonNo);
            throw new BizException("未找到对应的课表信息");
        }
        log.info("查询到课表信息: {}", JSON.toJSONString(timetable));
        return timetable;
    }

    /**
     * 根据补课的lesson转换获取线上补课实体
     *
     * @param lessonNo
     * @return com.yuedu.ydsf.eduConnect.jw.entity.BCourseMakeUpOnline
     * <AUTHOR>
     * @date 2025/4/27 16:12
     */
    private BCourseMakeUpOnline getCourseMakeUpOnline(Long lessonNo) {
        String idStr = String.valueOf(lessonNo);
        String realIdStr = idStr.substring(idStr.length() - 15);
        log.info("解析线上补课ID: {}, 原始lessonNo: {}", realIdStr, lessonNo);

        BCourseMakeUpOnline makeUpOnline =
            bCourseMakeUpOnlineMapper.selectById(Long.valueOf(realIdStr));
        return makeUpOnline;
    }

    /**
     * 获取学生信息
     */
    private StudentVO getStudentInfo(Long studentId) {
        log.info("开始查询学生信息, studentId: {}", studentId);
        R<List<StudentVO>> studentInfoResult =
            remoteStudentService.getStudentListByIds(
                Collections.singletonList(studentId));
        log.info("查询学生信息结果: {}", JSON.toJSONString(studentInfoResult));

        if (!studentInfoResult.isOk() || CollUtil.isEmpty(studentInfoResult.getData())) {
            log.error("未找到学生信息, studentId: {}", studentId);
            throw new BizException("未找到学生信息");
        }

        StudentVO studentVO = studentInfoResult.getData().get(0);
        log.info("学生信息: {}", JSON.toJSONString(studentVO));
        return studentVO;
    }

    /**
     * 获取学生课次记录
     */
    private BClassTimeStudent getClassTimeStudent(Long lessonNo, Long studentId, Long storeId) {
        return getClassTimeStudent(lessonNo, studentId, storeId, false);
    }

    /**
     * 获取学生课次记录
     *
     * @param lessonNo 课次号
     * @param studentId 学生ID
     * @param storeId 门店ID
     * @param isReissue 是否为补签操作
     * @return BClassTimeStudent 学生课次记录
     */
    private BClassTimeStudent getClassTimeStudent(Long lessonNo, Long studentId, Long storeId, boolean isReissue) {
        log.info("开始查询学生课次记录, lessonNo: {}, studentId: {}, isReissue: {}", lessonNo, studentId, isReissue);

        // 1. 首先查找未手动移除的记录
        BClassTimeStudent classTimeStudent =
            this.getOne(
                Wrappers.<BClassTimeStudent>lambdaQuery()
                    .eq(BClassTimeStudent::getLessonNo, lessonNo)
                    .eq(BClassTimeStudent::getStudentId, studentId)
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .ne(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode()))); // 过滤手动移除的学员

        // 2. 如果未找到未手动移除的记录，查找已手动移除的记录
        if (classTimeStudent == null) {
            log.info(
                "未找到未手动移除的学生课次记录，尝试查找已手动移除的记录, lessonNo: {}, studentId: {}",
                lessonNo, studentId);

            BClassTimeStudent removedStudent = this.getOne(
                Wrappers.<BClassTimeStudent>lambdaQuery()
                    .eq(BClassTimeStudent::getLessonNo, lessonNo)
                    .eq(BClassTimeStudent::getStudentId, studentId)
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .eq(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode()))); // 查找已手动移除的学员

            if (removedStudent != null) {
                log.info("找到已手动移除的学生记录，重新激活该记录, lessonNo: {}, studentId: {}, isReissue: {}",
                    lessonNo, studentId, isReissue);

                // 3. 重新激活已手动移除的学员记录
                removedStudent.setIsManuallyRemoved(Integer.parseInt(YesNoEnum.NO.getCode()));
                removedStudent.setCheckInStatus(
                    CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 重置为未出勤状态

                // 根据操作类型设置学员类型，避免显示临加标签
                if (isReissue) {
                    // 补签操作：设置为添加考勤学生，标识这是通过补签重新激活的学员
                    removedStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_3.code); // 3-添加考勤学生
                    log.info("补签操作重新激活学员，设置学员类型为添加考勤学生(3), studentId: {}", studentId);
                } else {
                    // 正常签到操作：设置为班级学生，表示正常的班级成员
                    removedStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_1.code); // 1-班级学生
                    log.info("正常签到操作重新激活学员，设置学员类型为班级学生(1), studentId: {}", studentId);
                }

                // 清除来源课次编号，避免数据关联混乱
                removedStudent.setSourceLessonNo(null);

                boolean updateResult = this.updateById(removedStudent);
                if (!updateResult) {
                    log.error("重新激活已手动移除的学生记录失败, id: {}", removedStudent.getId());
                    throw new BizException("重新激活学生记录失败");
                }

                log.info("成功重新激活已手动移除的学生记录, lessonNo: {}, studentId: {}, 新学员类型: {}",
                    lessonNo, studentId, removedStudent.getStudentType());
                classTimeStudent = removedStudent;
            }
        }

        if (classTimeStudent == null) {
            log.error("未找到学生课次记录, lessonNo: {}, studentId: {}", lessonNo, studentId);
            throw new BizException("未找到学生课次记录");
        }

        log.info("查询到学生课次记录: {}", JSON.toJSONString(classTimeStudent));
        return classTimeStudent;
    }

    /**
     * 检查学生是否已签到(用于补签)
     */
    private void checkStudentAlreadyCheckedIn(
        BClassTimeStudent classTimeStudent, String studentName) {
        if (classTimeStudent.getCheckInStatus() != null
            && classTimeStudent.getCheckInStatus()
            .equals(CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
            log.warn("学生[{}]已完成考勤，无需补签", studentName);
            throw new BizException(String.format("学员[%s]已完成考勤，无需补签", studentName));
        }
        log.info("学生[{}]考勤状态检查通过，可以进行补签", studentName);
    }

    /**
     * 检查学生剩余课时
     */
    private R<ClassStudentErrorVO> checkRemainingLessons(
        StudentVO studentVO, boolean needDeductCourseHours, Integer forceCheckIn) {
        Integer remainingLessons = studentVO.getCourseHours();
        if (remainingLessons == null) {
            remainingLessons = 0;
            log.warn("学生[{}]剩余课时为null，设置为默认值0", studentVO.getName());
        }
        log.info(
            "学生[{}]剩余课时: {}, 是否需要扣减: {}", studentVO.getName(), remainingLessons,
            needDeductCourseHours);

        // 课时不足且需要扣减课时时，确认是否强制签到
        if (remainingLessons <= 0
            && needDeductCourseHours
            && Objects.equals(Integer.valueOf(YesNoEnum.NO.getCode()), forceCheckIn)) {
            String studentName = studentVO.getName() != null ? studentVO.getName() : "未知";
            String studentPhone = studentVO.getPhone() != null ? studentVO.getPhone() : "未知";

            log.warn("学员[{} {}]剩余课次为0，需要强制考勤", studentName, studentPhone);
            // 构造错误响应对象
            ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
            errorVO.setCode(ClassStudentErrorEnum.REMAINING_LESSONS_ERROR.getCode());
            errorVO.setMsg(String.format("学员[%s %s]剩余课次为0，是否需要强制考勤", studentName,
                studentPhone));

            // 返回包装后的响应对象
            return R.ok(errorVO);
        }
        log.info("学生[{}]剩余课时检查通过", studentVO.getName());
        return null;
    }

    /**
     * 更新学生考勤状态
     */
    private void updateStudentCheckInStatus(BClassTimeStudent classTimeStudent, boolean isReissue) {
        log.info("开始更新学生考勤状态, 是否补签: {}", isReissue);
        classTimeStudent.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_1.code);
        classTimeStudent.setCheckInType(
            isReissue ? CheckInTypeEnum.CHECK_IN_TYPE_1.code
                : CheckInTypeEnum.CHECK_IN_TYPE_0.code);
        classTimeStudent.setCheckInTime(LocalDateTime.now());
        classTimeStudent.setCheckInCreateBy(SecurityUtils.getUser().getName());

        boolean updateResult = this.updateById(classTimeStudent);
        log.info("更新学生考勤状态结果: {}, 考勤类型: {}", updateResult,
            isReissue ? "补签" : "正常签到");

        if (!updateResult) {
            log.error("更新学生考勤状态失败");
            throw new BizException("更新学生考勤状态失败");
        }
    }

    /**
     * 使用乐观锁更新课次学生表状态为未签到
     */
    private boolean updateToUnCheckIn(BClassTimeStudent classTimeStudent) {
        log.info("开始更新学生考勤状态为未签到, lessonNo: {}, studentId:{}",
            classTimeStudent.getLessonNo(), classTimeStudent.getStudentId());
        classTimeStudent.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_0.code);
        classTimeStudent.setCheckInType(null);
        classTimeStudent.setCheckInTime(null);
        classTimeStudent.setCheckInCreateBy(null);
        return this.update(classTimeStudent,
            Wrappers.<BClassTimeStudent>lambdaUpdate()
                .eq(BClassTimeStudent::getId, classTimeStudent.getId())
                .eq(BClassTimeStudent::getCheckInStatus,
                    CheckInStatusEnum.CHECK_IN_STATUS_1.code));
    }

    /**
     * 取消考勤归还课时
     */
    public void returnCourseHours(Long studentId, Long timetableId) {
        log.info("开始执行课时归还, studentId: {}, timetableId: {}", studentId, timetableId);
        try {
            CourseHoursCancelDTO courseHoursCancelDTO = new CourseHoursCancelDTO();
            courseHoursCancelDTO.setStoreId(StoreContextHolder.getStoreId());
            courseHoursCancelDTO.setSchoolId(StoreContextHolder.getSchoolId());
            courseHoursCancelDTO.setStudentId(studentId);
            courseHoursCancelDTO.setTimetableId(timetableId);

            // 同步调用远程服务进行课时归还
            R courseHoursResult = remoteStudentService.courseHoursCancel(courseHoursCancelDTO);
            log.info("课时归还结果: {}", JSON.toJSONString(courseHoursResult));

            if (!courseHoursResult.isOk()) {
                log.error("课时归还失败: {}", courseHoursResult.getMsg());
                throw new BizException("课时归还失败: " + courseHoursResult.getMsg());
            }
            log.info("课时归还成功, studentId: {}", studentId);
        } catch (Exception e) {
            log.error("课时归还异常: {}, 学生ID: {}", e.getMessage(), studentId, e);
            throw new BizException("课时归还异常: " + e.getMessage());
        }
    }

    /**
     * 考勤管理列表
     *
     * @param classTimeStudentQuery
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.yuedu.ydsf.eduConnect.jw.api.vo.AttendanceManagementVO>
     * <AUTHOR>
     * @date 2025/2/26 17:45
     */
    @Override
    public Page<AttendanceManagementVO> attendanceManagementList(
        Page pageArgs, BClassTimeStudentQuery classTimeStudentQuery) {
        log.info("开始查询考勤管理列表, 查询条件: {}",
            JSON.toJSONString(classTimeStudentQuery));

        Page<AttendanceManagementVO> groupPage = null;

        if (StringUtils.isNotBlank(classTimeStudentQuery.getLessonName())) {

            classTimeStudentQuery.setStoreId(StoreContextHolder.getStoreId());

            CoursePublishQuery coursePublishQuery = new CoursePublishQuery();
            coursePublishQuery.setLessonName(classTimeStudentQuery.getLessonName());
            // 调用远程服务获取符合课节名称条件的课节信息
            R<List<LessonEntity>> lessonResult = remoteLessonService.getLessonByName(
                coursePublishQuery);

            if (!lessonResult.isOk() || CollectionUtils.isEmpty(lessonResult.getData())) {
                log.info("未找到匹配课节名称[{}]的课节信息", classTimeStudentQuery.getLessonName());
                return new Page<>(pageArgs.getCurrent(), pageArgs.getSize());
            }

            // 提取课程ID和课节序号对，用于构建查询条件
            List<LessonEntity> matchedLessons = lessonResult.getData();
            log.info("找到匹配课节名称[{}]的课节数量: {}", classTimeStudentQuery.getLessonName(),
                matchedLessons.size());

            // 构建基础查询条件
            LambdaQueryWrapper<Timetable> queryWrapper =
                Wrappers.lambdaQuery(Timetable.class)
                    .eq(Timetable::getStoreId, classTimeStudentQuery.getStoreId())
                    .lt(Timetable::getClassEndDateTime, LocalDateTime.now())
                    .orderByDesc(Timetable::getClassStartDateTime);

            // 由于可能有多个匹配的课节，我们需要使用OR条件
            if (!matchedLessons.isEmpty()) {
                queryWrapper.and(wrapper -> {
                    for (LessonEntity lesson : matchedLessons) {
                        wrapper.or(w -> w
                            .eq(Timetable::getCourseId, lesson.getCourseId())
                            .eq(Timetable::getLessonOrder, lesson.getLessonOrder())
                        );
                    }
                });
            }

            List<Timetable> timetablePage = timetableMapper.selectList(queryWrapper);

            if (CollectionUtils.isEmpty(timetablePage)) {
                return new Page<>(pageArgs.getCurrent(), pageArgs.getSize());
            }

            groupPage =
                baseMapper.selectAttendanceGroupByLessonName(
                    pageArgs, classTimeStudentQuery, StoreContextHolder.getStoreId(),
                    timetablePage.stream().map(Timetable::getId).toList());

        } else {

            groupPage =
                baseMapper.selectAttendanceGroupByLessonNo(
                    pageArgs, classTimeStudentQuery, StoreContextHolder.getStoreId());
        }

        // 1. 分页查询课次学生记录,按lessonNo分组
//        Page<AttendanceManagementVO> groupPage =
//            baseMapper.selectAttendanceGroupByLessonNo(
//                pageArgs, classTimeStudentQuery, StoreContextHolder.getStoreId());

        if (CollUtil.isEmpty(groupPage.getRecords())) {
            log.info("未查询到考勤记录");
            return new Page<>(pageArgs.getCurrent(), pageArgs.getSize());
        }

        // 2. 获取所有涉及的课次号
        List<Long> lessonNos =
            groupPage.getRecords().stream()
                .map(AttendanceManagementVO::getLessonNo)
                .collect(Collectors.toList());

        // 3. 查询课表信息
        List<Timetable> timetables =
            timetableMapper.selectList(
                Wrappers.<Timetable>lambdaQuery()
                    .in(Timetable::getLessonNo, lessonNos)
                    .orderByDesc(Timetable::getClassStartDateTime));

        if (CollUtil.isEmpty(timetables)) {
            log.warn("未找到对应的课表信息, lessonNos: {}", lessonNos);
            return new Page<>(pageArgs.getCurrent(), pageArgs.getSize());
        }

        // 4. 远程调用获取课节信息
        List<LessonOrderDTO> lessonOrderDTOs =
            timetables.stream()
                .map(
                    t -> {
                        LessonOrderDTO dto = new LessonOrderDTO();
                        dto.setCourseId(t.getCourseId());
                        dto.setLessonOrderList(Collections.singletonList(t.getLessonOrder()));
                        return dto;
                    })
                .collect(Collectors.toList());

        R<List<LessonVO>> lessonResult = remoteLessonService.getLessonListByOrder(
            lessonOrderDTOs);
        log.info("远程获取课节信息结果: {}", JSON.toJSONString(lessonResult));

        Map<String, LessonVO> lessonMap = new HashMap<>();
        if (lessonResult.isOk() && CollUtil.isNotEmpty(lessonResult.getData())) {
            lessonMap =
                lessonResult.getData().stream()
                    .collect(
                        Collectors.toMap(
                            lesson -> lesson.getCourseId() + "_" + lesson.getLessonOrder(),
                            Function.identity(),
                            (a, b) -> a));
        }

        // 5. 如果有课节名称搜索条件，过滤不匹配的课表
        if (StringUtils.isNotBlank(classTimeStudentQuery.getLessonName())) {
            Map<String, LessonVO> finalLessonMap = lessonMap;
            timetables =
                timetables.stream()
                    .filter(
                        t -> {
                            LessonVO lessonVO =
                                finalLessonMap.get(t.getCourseId() + "_" + t.getLessonOrder());
                            return lessonVO != null
                                && lessonVO.getLessonName() != null
                                && lessonVO.getLessonName()
                                .contains(classTimeStudentQuery.getLessonName());
                        })
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(timetables)) {
                log.info("课节名称过滤后无数据");
                return new Page<>(pageArgs.getCurrent(), pageArgs.getSize());
            }
        }

        // 6. 获取其他关联数据
        Map<Long, EmployeeVO> teacherVOMap = timetableChangeManager.fetchTeacherVOMap(
            timetables);
        Map<Long, ClassVO> classVOMap = timetableChangeManager.fetchClassVOMap(timetables);

        // 7. 组装返回数据
        List<AttendanceManagementVO> resultList = new ArrayList<>();
        Map<Long, AttendanceManagementVO> groupMap =
            groupPage.getRecords().stream()
                .collect(
                    Collectors.toMap(
                        AttendanceManagementVO::getLessonNo, Function.identity(), (a, b) -> a));

        for (Timetable timetable : timetables) {
            AttendanceManagementVO attendanceManagementVO = groupMap.get(
                timetable.getLessonNo());
            if (attendanceManagementVO == null) {
                continue;
            }

            AttendanceManagementVO vo = new AttendanceManagementVO();
            vo.setLessonNo(timetable.getLessonNo());

            // 设置课节名称
            LessonVO lessonVO = lessonMap.get(
                timetable.getCourseId() + "_" + timetable.getLessonOrder());
            if (lessonVO != null) {
                vo.setLessonName(lessonVO.getLessonName());
            }

            // 设置班级名称
            ClassVO classVO = classVOMap.get(timetable.getClassId());
            if (classVO != null) {
                vo.setClassName(classVO.getCName());
            }

            // 设置指导老师
            EmployeeVO teacherVO = teacherVOMap.get(timetable.getTeacherId());
            if (teacherVO != null) {
                vo.setTeacherName(teacherVO.getName());
            }

            // 格式化上课时间
            try {
                String fullClassTimeStr =
                    String.format(
                        "%s (%s) %s-%s",
                        timetable.getClassStartDateTime()
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        timetable
                            .getClassStartDateTime()
                            .getDayOfWeek()
                            .getDisplayName(TextStyle.SHORT, Locale.CHINESE),
                        timetable.getClassStartDateTime()
                            .format(DateTimeFormatter.ofPattern("HH:mm")),
                        timetable.getClassEndDateTime()
                            .format(DateTimeFormatter.ofPattern("HH:mm")));
                vo.setFullClassTimeStr(fullClassTimeStr);
            } catch (Exception e) {
                log.error("格式化上课时间异常, timetableId: {}", timetable.getId(), e);
            }

            // 设置考勤统计
            vo.setCheckedInCount(attendanceManagementVO.getCheckedInCount());
            vo.setTotalCount(attendanceManagementVO.getTotalCount());

            resultList.add(vo);
        }

        // 8. 返回分页结果
        Page<AttendanceManagementVO> result = new Page<>(pageArgs.getCurrent(),
            pageArgs.getSize());
        result.setTotal(groupPage.getTotal());
        result.setRecords(resultList);

        log.info("考勤管理列表查询完成, 总记录数: {}, 当前页记录数: {}", result.getTotal(),
            resultList.size());
        return result;
    }

    /**
     * 分析调课记录，返回学生在指定课节的有效性
     *
     * @param changes  调课记录列表
     * @param lessonNo 当前课节号
     * @return Map<学生ID, 是否在当前课节有效>
     */
    private Map<Long, Boolean> analyzeTimetableChanges(List<TimetableChange> changes,
        Long lessonNo) {
        Map<Long, Long> studentCurrentLessonMap = new HashMap<>();
        Map<Long, Boolean> studentValidityMap = new HashMap<>();

        log.info("开始分析调课链, 当前课节: {}, 调课记录数: {}", lessonNo, changes.size());

        // 按创建时间排序处理调课记录
        changes.stream()
            .sorted(Comparator.comparing(TimetableChange::getCreateTime))
            .forEach(
                change -> {
                    // 更新学生当前所在课节
                    studentCurrentLessonMap.put(change.getStudentId(),
                        change.getTargetLessonNo());

                    // 判断学生是否最终在当前课节
                    boolean isValid = change.getTargetLessonNo().equals(lessonNo);
                    studentValidityMap.put(change.getStudentId(), isValid);

                    log.debug(
                        "学生调课记录处理 - 学生ID: {}, 目标课节: {}, 在当前课节是否有效: {}",
                        change.getStudentId(),
                        change.getTargetLessonNo(),
                        isValid);
                });

        log.info("调课链分析完成, 学生有效性映射: {}", studentValidityMap);
        return studentValidityMap;
    }

    /**
     * 根据课次编号获取已出勤学生列表
     *
     * @param lessonNo
     * @param storeId
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.BClassTimeStudentVO>
     * <AUTHOR>
     * @date 2025/3/11 10:38
     */
    @Override
    public List<StudentCheckInInfoVO> getCheckedInStudentsByLessonNo(Long lessonNo, Long
        storeId) {
        log.info("开始查询课次[{}]门店[{}]的已出勤学生列表", lessonNo, storeId);

        try {
            // 参数校验
            if (lessonNo == null || storeId == null) {
                log.error("查询已出勤学生列表参数错误, lessonNo={}, storeId={}", lessonNo,
                    storeId);
                return Collections.emptyList();
            }

            // 构建查询条件
            LambdaQueryWrapper<BClassTimeStudent> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper
                .eq(BClassTimeStudent::getLessonNo, lessonNo)
                .eq(BClassTimeStudent::getStoreId, storeId)
                .eq(BClassTimeStudent::getCheckInStatus,
                    CheckInStatusEnum.CHECK_IN_STATUS_1.code)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode())); // 过滤手动移除的学员

            // 执行查询
            List<BClassTimeStudent> studentList = this.list(queryWrapper);

            if (CollectionUtils.isEmpty(studentList)) {
                log.info("课次[{}]门店[{}]暂无已出勤学生", lessonNo, storeId);
                return Collections.emptyList();
            }

            List<Integer> studentIds =
                studentList.stream()
                    .map(BClassTimeStudent::getStudentId)
                    .map(e -> Integer.valueOf(e.toString()))
                    .collect(Collectors.toList());
            List<StudentCheckInInfoVO> studentInfoList = new ArrayList<>();

            remoteAndStudentInfo(studentIds, studentList, studentInfoList);

            log.info("课次[{}]门店[{}]查询到[{}]个已出勤学生", lessonNo, storeId,
                studentInfoList.size());
            return studentInfoList;

        } catch (Exception e) {
            log.error("查询课次[{}]门店[{}]已出勤学生列表异常", lessonNo, storeId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据课次编号获取已绑定答题设备的学生列表
     *
     * @param lessonNo 课次编号
     * @param storeId 门店ID
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO>
     * <AUTHOR>
     * @date 2025/7/15
     */
    @Override
    public List<StudentCheckInInfoVO> getBoundClickerStudentsByLessonNo(Long lessonNo, Long storeId) {
        log.info("开始查询课次[{}]门店[{}]的已绑定答题设备学生列表", lessonNo, storeId);

        try {
            // 参数校验
            if (lessonNo == null || storeId == null) {
                log.error("查询已绑定答题设备学生列表参数错误, lessonNo={}, storeId={}", lessonNo, storeId);
                return Collections.emptyList();
            }

            // 构建查询条件：查询已绑定答题设备的学生
            LambdaQueryWrapper<BClassTimeStudent> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper
                .eq(BClassTimeStudent::getLessonNo, lessonNo)
                .eq(BClassTimeStudent::getStoreId, storeId)
                .isNotNull(BClassTimeStudent::getReceiverSnNumber)
                .isNotNull(BClassTimeStudent::getClickerSnNumber)
                .ne(BClassTimeStudent::getReceiverSnNumber, "")
                .ne(BClassTimeStudent::getClickerSnNumber, "")
                .ne(BClassTimeStudent::getIsManuallyRemoved, Integer.parseInt(YesNoEnum.YES.getCode())); // 过滤手动移除的学员

            // 执行查询
            List<BClassTimeStudent> studentList = this.list(queryWrapper);

            if (CollectionUtils.isEmpty(studentList)) {
                log.info("课次[{}]门店[{}]暂无已绑定答题设备的学生", lessonNo, storeId);
                return Collections.emptyList();
            }

            // 过滤掉"绑定中"状态的学生，只返回真正已绑定完成的学生
            List<BClassTimeStudent> boundStudents = studentList.stream()
                .filter(student ->
                    !CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(student.getReceiverSnNumber()) &&
                    !CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(student.getClickerSnNumber()))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(boundStudents)) {
                log.info("课次[{}]门店[{}]暂无已完成绑定答题设备的学生", lessonNo, storeId);
                return Collections.emptyList();
            }

            List<Integer> studentIds = boundStudents.stream()
                .map(BClassTimeStudent::getStudentId)
                .map(e -> Integer.valueOf(e.toString()))
                .collect(Collectors.toList());
            List<StudentCheckInInfoVO> studentInfoList = new ArrayList<>();

            remoteAndStudentInfo(studentIds, boundStudents, studentInfoList);

            log.info("课次[{}]门店[{}]查询到[{}]个已绑定答题设备的学生", lessonNo, storeId, studentInfoList.size());
            return studentInfoList;

        } catch (Exception e) {
            log.error("查询课次[{}]门店[{}]已绑定答题设备学生列表异常", lessonNo, storeId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量根据lessonNo获取对应的出勤人数
     *
     * @param bClassTimeStudentDTO
     * @return java.util.List<com.yuedu.ydsf.eduConnect.jw.api.vo.CheckInStudentVO.StudentCheckInInfoVO>
     * <AUTHOR>
     * @date 2025/3/19 9:14
     */
    @Override
    public List<StudentCheckInInfoVO> getCheckedInStudentsByLessonNoBatch(
        BClassTimeStudentDTO bClassTimeStudentDTO) {

        List<Long> lessonNos = bClassTimeStudentDTO.getLessonNos();
        Long storeId = bClassTimeStudentDTO.getStoreId();

        // 使用并行流来处理每个lessonNo
        return lessonNos.parallelStream()
            .flatMap(
                lessonNo -> {
                    try {
                        List<StudentCheckInInfoVO> checkedInStudents =
                            getCheckedInStudentsByLessonNo(lessonNo, storeId);
                        return checkedInStudents.stream();
                    } catch (Exception e) {
                        log.error("lessonNo: {} 获取出勤人数异常！", lessonNo, e);
                        return Stream.empty();
                    }
                })
            .collect(Collectors.toList());
    }

    /**
     * 绑定答题器
     *
     * @param bClassTimeStudentDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/4/11 14:31
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R bindClicker(BClassTimeStudentDTO bClassTimeStudentDTO) {
        Long lessonNo = bClassTimeStudentDTO.getLessonNo();
        Long studentId = bClassTimeStudentDTO.getStudentId();
        Long storeId = StoreContextHolder.getStoreId();

        log.info("开始绑定答题器, lessonNo: {}, studentId: {}, storeId: {}", lessonNo, studentId,
            storeId);

        // 1. 查询课表信息
        Timetable timetable = getTimetableInfo(lessonNo, storeId);
        log.info("获取到课表信息: {}", JSON.toJSONString(timetable));

        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(timetable.getClassEndDateTime())) {
            throw new BizException("课程已结束，不允许操作！");
        }
        // 3. 获取学生信息
        StudentVO studentVO = getStudentInfo(studentId);
        log.info("获取到学生信息: {}", JSON.toJSONString(studentVO));

        // 4. 获取学生课次记录
        BClassTimeStudent classTimeStudent = getClassTimeStudent(lessonNo, studentId, storeId);
        log.info("获取到学生课次记录: {}", JSON.toJSONString(classTimeStudent));

        R result = bInteractionReceiverService.bindClicker(timetable, studentVO, classTimeStudent);
        log.info("绑定答题器结果: {}", JSON.toJSONString(result));

        return result;
    }

    /**
     * 解绑答题器
     *
     * @param bClassTimeStudentDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/4/11 14:47
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R unBindClicker(BClassTimeStudentDTO bClassTimeStudentDTO) {
        Long lessonNo = bClassTimeStudentDTO.getLessonNo();
        Long studentId = bClassTimeStudentDTO.getStudentId();
        Long storeId = StoreContextHolder.getStoreId();

        log.info("开始解绑答题器, lessonNo: {}, studentId: {}, storeId: {}", lessonNo, studentId,
            storeId);

        // 1. 查询课表信息
        Timetable timetable = getTimetableInfo(lessonNo, storeId);
        log.info("获取到课表信息: {}", JSON.toJSONString(timetable));

        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(timetable.getClassEndDateTime())) {
            throw new BizException("课程已结束，不允许操作！");
        }
        // 2. 发送RTM进行解绑
        R result = bInteractionReceiverService.unbindClicker(timetable, studentId);
        if (Objects.nonNull(result)) {
            return result;
        }
        return R.ok(new ClassStudentErrorVO());
    }

    /**
     * 根据排课类型还有业务id查询课表下是否存在已签到的学生
     *
     * @param code
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2025/4/17 10:35
     */
    @Override
    public Timetable checkAlreadyCheckIn(Integer code, Long id) {
        // 根据排课类型和业务id查询课表信息
        LambdaQueryWrapper<Timetable> timetableWrapper = Wrappers.lambdaQuery();
        timetableWrapper
            .eq(Timetable::getCourseType, code)
            .eq(Timetable::getCoursePlanId, id)
            .eq(Timetable::getStoreId, StoreContextHolder.getStoreId());
        Timetable timetable = timetableMapper.selectOne(timetableWrapper);
        if (Objects.isNull(timetable)) {
            throw new BizException("未找到相关课表信息");
        }
        checkTimeableCheckIn(timetable);
        return timetable;
    }

    /**
     * 校验学生是否存在已签到
     *
     * @param timetable
     * @return void
     * <AUTHOR>
     * @date 2025/4/17 10:48
     */
    private void checkTimeableCheckIn(Timetable timetable) {
        // 查询出来lessonno在查询classtimestudent看是否存在已签到的学生存在抛异常
        LambdaQueryWrapper<BClassTimeStudent> studentWrapper = Wrappers.lambdaQuery();
        studentWrapper
            .eq(BClassTimeStudent::getLessonNo, timetable.getLessonNo())
            .eq(BClassTimeStudent::getStoreId, timetable.getStoreId())
            .eq(BClassTimeStudent::getCheckInStatus, CheckInStatusEnum.CHECK_IN_STATUS_1.code);

        long count = this.count(studentWrapper);
        if (count > 0) {
            throw new BizException("该课次已存在已签到出勤学生，不允许操作");
        }
    }

    /**
     * 校验删除的排课之前是否存在签到的学生
     *
     * @param timetableOld
     * @return void
     * <AUTHOR>
     * @date 2025/4/17 10:47
     */
    @Override
    public void checkLiveAlreadyCheckIn(Timetable timetableOld) {
        checkTimeableCheckIn(timetableOld);
    }

    /**
     * 移除班级学生
     *
     * @param timetable
     * @param classId
     * @return void
     * <AUTHOR>
     * @date 2025/4/18 10:10
     */
    @Override
    public void removeClassStudent(Timetable timetable, Long classId) {
        // 根据classid查询班级学生
        R<List<StudentVO>> classStudentsResult =
            remoteStudentService.getListByClassId(classId.intValue());
        log.debug("移除班级学生查询班级学生结果: {}", JSON.toJSONString(classStudentsResult));

        List<StudentVO> studentVOList = new ArrayList<>();
        if (classStudentsResult.isOk() && CollUtil.isNotEmpty(classStudentsResult.getData())) {
            studentVOList = classStudentsResult.getData();
        }
        // 根据学生id还有课表相关信息进行移除操作
        this.remove(
            Wrappers.lambdaQuery(BClassTimeStudent.class)
                .eq(BClassTimeStudent::getLessonNo, timetable.getLessonNo())
                .eq(BClassTimeStudent::getStoreId, timetable.getStoreId())
                .ne(BClassTimeStudent::getCheckInStatus, CheckInStatusEnum.CHECK_IN_STATUS_1.code)
                .in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(studentVOList),
                    BClassTimeStudent::getStudentId,
                    studentVOList.stream().map(StudentVO::getUserId).toList()));
    }

    @Override
    public Map<Long, Map<Long, Long>> getLessonStudentCount(List<Long> lessonNoList) {
        return this.list(
                Wrappers.lambdaQuery(BClassTimeStudent.class)
                    .in(BClassTimeStudent::getLessonNo, lessonNoList)
                    .eq(BClassTimeStudent::getCheckInStatus,
                        CheckInStatusEnum.CHECK_IN_STATUS_1.code)
                    .ne(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode()))) // 过滤手动移除的学员
            .stream()
            .collect(Collectors.groupingBy(
                BClassTimeStudent::getStoreId,
                Collectors.groupingBy(
                    BClassTimeStudent::getLessonNo,
                    Collectors.counting()
                )
            ));

    }

    /**
     * 批量解绑答题器
     *
     * @param bClassTimeStudentDTO
     * @return void
     * <AUTHOR>
     * @date 2025/5/7 8:53
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUnBindClicker(BClassTimeStudentDTO bClassTimeStudentDTO) {
        Long lessonNo = bClassTimeStudentDTO.getLessonNo();
        Long storeId = bClassTimeStudentDTO.getStoreId();

        log.info("开始批量解绑答题器, lessonNo: {}, storeId: {}", lessonNo, storeId);

        // 1. 查询课表信息，获取上课开始和结束时间
        Timetable timetable = getTimetableInfo(lessonNo, storeId);
        log.info("获取到课表信息: {}", JSON.toJSONString(timetable));

        // 2. 判断课程是否已结束
        LocalDateTime now = LocalDateTime.now();
        boolean isClassEnded = now.isAfter(timetable.getClassEndDateTime());
        log.info("课程是否已结束: {}", isClassEnded);

        // 3. 如果课程未结束，需要清空BClassTimeStudent表中对应lessonNo和storeId的绑定信息
        if (!isClassEnded) {
            // 查询符合条件的所有学生记录
            List<BClassTimeStudent> students =
                this.list(
                    Wrappers.lambdaQuery(BClassTimeStudent.class)
                        .eq(BClassTimeStudent::getLessonNo, lessonNo)
                        .eq(BClassTimeStudent::getStoreId, storeId)
                        .isNotNull(BClassTimeStudent::getClickerSnNumber));

            if (CollUtil.isNotEmpty(students)) {
                log.info("需要解绑的学生数量: {}", students.size());
                // 使用LambdaUpdateWrapper只更新需要修改的字段
                for (BClassTimeStudent student : students) {
                    boolean updateResult = update(
                        Wrappers.<BClassTimeStudent>lambdaUpdate()
                            .eq(BClassTimeStudent::getId, student.getId())
                            .set(BClassTimeStudent::getClickerSnNumber, null)
                            .set(BClassTimeStudent::getReceiverSnNumber, null)
                    );
                    if (!updateResult) {
                        log.warn("清空学生答题器绑定信息失败, 学生ID: {}", student.getStudentId());
                    }
                }
                log.info("已清空学生答题器绑定信息");
            }
        }

        // 4. 无论课程是否结束，都需要通过声网点对点发送批量解绑消息
        // 获取当前教室启用的接收器
        BInteractionReceiverVO receiverVO =
            bInteractionReceiverService.currentClassroomReceiver(timetable.getClassroomId());
        if (receiverVO == null) {
            log.warn("未找到教室对应的接收器信息, 教室ID: {}", timetable.getClassroomId());
            return;
        }

        // 获取设备ID
        String deviceId =
            bInteractionReceiverService.getDeviceIdFromCache(
                timetable.getClassroomId(), receiverVO.getSnNumber());
        if (StringUtils.isBlank(deviceId)) {
            log.warn("设备ID获取失败 - 教室ID：{}，接收器SN：{}", timetable.getClassroomId(),
                receiverVO.getSnNumber());
            return;
        }

        // 构建批量解绑RTM消息（使用特定的消息类型标识批量操作）
        InteractionSendTypeEnum typeEnum =
            InteractionSendTypeEnum.getByBussinessType(InteractionTypeEnum.INTERACTION_TYPE_4.code);
        AgoraUpdateRoomPropertiesDTO updateRoomPropertiesDTO = new AgoraUpdateRoomPropertiesDTO();
        AgoraUpdateRoomPropertiesDTO.v v = new AgoraUpdateRoomPropertiesDTO.v();
        // 批量解绑特殊标识，使用特定值标记为批量操作
        v.setH(JwConstant.ALL_UNBIND);
        v.setY(timetable.getId().intValue());
        updateRoomPropertiesDTO.setV(v);
        updateRoomPropertiesDTO.setK(
            (typeEnum.getSendTerminalEnum().getCode()) | typeEnum.getCode());

        // 发送RTM消息
        bInteractionReceiverService.sendPeerMessage(deviceId, updateRoomPropertiesDTO);
        log.info("批量解绑答题器RTM消息发送完成 - 课表ID：{}", timetable.getId());
    }

    /**
     * 批量绑定答题器
     *
     * @param bClassTimeStudentDTO
     * @return com.yuedu.ydsf.common.core.util.R<BatchBindClickerResultVO>
     * <AUTHOR>
     * @date 2025/7/8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<BatchBindClickerResultVO> batchBindClicker(BClassTimeStudentDTO bClassTimeStudentDTO) {
        Long lessonNo = bClassTimeStudentDTO.getLessonNo();
        Long storeId = bClassTimeStudentDTO.getStoreId();

        log.info("开始批量绑定答题器, lessonNo: {}, storeId: {}", lessonNo, storeId);

        // 检查课程级别的操作锁定
        String lockKey = CacheConstants.BATCH_BIND_CLICKER_LOCK + lessonNo;
        long lockExpireTime = 10L; // 10秒锁定期

        // 检查是否已被锁定
        Long remainingTtl = redisTemplate.getExpire(lockKey, TimeUnit.SECONDS);
        if (Objects.nonNull(remainingTtl) && remainingTtl > 0) {
            log.warn("课程 {} 正在进行批量绑定答题器操作，剩余锁定时间: {}秒", lessonNo,
                remainingTtl);
            BatchBindClickerResultVO result = new BatchBindClickerResultVO();
            result.setRtmSendSuccess(false);
            result.setTotalStudentCount(0);
            result.setCourseHoursDeductSuccessCount(0);
            result.setCourseHoursDeductErrorCount(0);
            result.setCourseHoursErrorStudents(new ArrayList<>());
            String errorMessage = "答题器批量绑定中，请稍后重试";
            result.setRtmMessage(errorMessage);
            return R.failed(result, errorMessage);
        }

        // 设置锁定
        String lockValue = String.valueOf(System.currentTimeMillis());
        Boolean lockSet = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(lockExpireTime));
        if (Boolean.FALSE.equals(lockSet)) {
            // 锁定失败，可能是并发情况下其他线程已经设置了锁
            log.warn("课程 {} 批量绑定答题器操作锁定失败，可能存在并发操作", lessonNo);
            BatchBindClickerResultVO result = new BatchBindClickerResultVO();
            result.setRtmSendSuccess(false);
            result.setTotalStudentCount(0);
            result.setCourseHoursDeductSuccessCount(0);
            result.setCourseHoursDeductErrorCount(0);
            result.setCourseHoursErrorStudents(new ArrayList<>());
            String errorMessage = "答题器批量绑定中，请稍后重试";
            result.setRtmMessage(errorMessage);
            return R.failed(result, errorMessage);
        }

        log.info("课程 {} 批量绑定答题器操作锁定成功，锁定时间: {}秒", lessonNo, lockExpireTime);

        // 初始化返回结果
        BatchBindClickerResultVO result = new BatchBindClickerResultVO();
        result.setRtmSendSuccess(false);
        result.setTotalStudentCount(0);
        result.setCourseHoursDeductSuccessCount(0);
        result.setCourseHoursDeductErrorCount(0);
        result.setCourseHoursErrorStudents(new ArrayList<>());

        Timetable timetable = getTimetableInfo(lessonNo, storeId);
        log.info("获取到课表信息: {}", JSON.toJSONString(timetable));

        LocalDateTime now = LocalDateTime.now();
        boolean isClassEnded = now.isAfter(timetable.getClassEndDateTime());
        log.info("课程是否已结束: {}", isClassEnded);

        if (isClassEnded) {
            result.setRtmMessage("课程已结束，不允许批量绑定答题器！");
            return R.failed(result, result.getRtmMessage());
        }

        BInteractionReceiverVO receiverVO =
            bInteractionReceiverService.currentClassroomReceiver(timetable.getClassroomId());
        if (receiverVO == null) {
            redisTemplate.delete(lockKey);
            log.warn("未找到教室对应的接收器信息, 教室ID: {}", timetable.getClassroomId());
            result.setRtmMessage("未找到教室对应的接收器信息，无法批量绑定答题器！");
            return R.failed(result, result.getRtmMessage());
        }

        String deviceId = bInteractionReceiverService.getDeviceIdFromCache(
            timetable.getClassroomId(), receiverVO.getSnNumber());
        if (StringUtils.isBlank(deviceId)) {
            redisTemplate.delete(lockKey);
            log.warn("设备ID获取失败 - 教室ID：{}，接收器SN：{}",
                timetable.getClassroomId(), receiverVO.getSnNumber());
            result.setRtmMessage("设备ID获取失败，无法发送批量绑定指令！");
            return R.failed(result, result.getRtmMessage());
        }

        // 1. 获取应出勤学员列表
        log.info("开始获取应出勤学员列表");
        CheckInStudentVO checkInStudentVO = getCheckInStudent(lessonNo,
            CheckInEntryTypeEnum.CHECK_IN_ENTRY_TYPE_1.code, storeId);

        if (checkInStudentVO == null || CollectionUtils.isEmpty(
            checkInStudentVO.getStudents())) {
            log.info("课次 {} 没有应出勤学生", lessonNo);
            result.setTotalStudentCount(0);
        } else {
            List<StudentCheckInInfoVO> students = checkInStudentVO.getStudents();
            result.setTotalStudentCount(students.size());
            log.info("获取到应出勤学员数量: {}", students.size());

            // 2. 获取当前课程的课程类型
            Integer courseTypeId = getCourseTypeFromTimetable(timetable);
            if (courseTypeId == null) {
                log.warn("无法获取课程类型，跳过课时扣减处理, courseId: {}",
                    timetable.getCourseId());
                result.setCourseHoursDeductErrorCount(students.size());
                // 为所有学员添加错误信息
                for (StudentCheckInInfoVO student : students) {
                    ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
                    errorVO.setCode(-1);
                    errorVO.setMsg("无法获取课程类型信息，课时扣减失败");

                    BatchBindClickerResultVO.StudentCourseHoursErrorVO errorStudent = new BatchBindClickerResultVO.StudentCourseHoursErrorVO();
                    errorStudent.setStudentId(student.getStudentId());
                    errorStudent.setStudentName(student.getStudentName());
                    errorStudent.setStudentPhone(student.getStudentMobile());
                    errorStudent.setErrorInfo(errorVO);

                    result.getCourseHoursErrorStudents().add(errorStudent);
                }
            } else {
                // 3. 对每个学员进行课时扣减预校验和处理
                processCourseHoursDeduction(students, timetable, courseTypeId, result);
            }
        }

        // 3. 发送RTM批量绑定指令
        try {
            InteractionSendTypeEnum typeEnum =
                InteractionSendTypeEnum.getByBussinessType(
                    InteractionTypeEnum.INTERACTION_TYPE_3.code);
            AgoraUpdateRoomPropertiesDTO updateRoomPropertiesDTO = new AgoraUpdateRoomPropertiesDTO();
            AgoraUpdateRoomPropertiesDTO.v v = new AgoraUpdateRoomPropertiesDTO.v();
            v.setH(JwConstant.ALL_BIND);
            v.setY(timetable.getId().intValue());
            updateRoomPropertiesDTO.setV(v);
            int messageCode = (typeEnum.getSendTerminalEnum().getCode()) | typeEnum.getCode();
            updateRoomPropertiesDTO.setK(messageCode);
            bInteractionReceiverService.sendPeerMessage(deviceId, updateRoomPropertiesDTO);

            result.setRtmSendSuccess(true);
            result.setRtmMessage("批量绑定答题器指令已发送，请等待终端处理！");
            log.info("批量绑定答题器RTM消息发送完成 - 课表ID：{}", timetable.getId());
        } catch (Exception e) {
            log.error("发送RTM批量绑定指令失败", e);
            result.setRtmSendSuccess(false);
            result.setRtmMessage("发送RTM批量绑定指令失败: " + e.getMessage());
        }

        log.info("批量绑定答题器处理完成 - 总学员数: {}, 课时扣减成功: {}, 课时扣减异常: {}",
            result.getTotalStudentCount(), result.getCourseHoursDeductSuccessCount(),
            result.getCourseHoursDeductErrorCount());

        return R.ok(result);
    }

    /**
     * 处理课时扣减逻辑
     *
     * @param students     应出勤学员列表
     * @param timetable    课表信息
     * @param courseTypeId 课程类型ID
     * @param result       批量绑定结果对象
     */
    private void processCourseHoursDeduction(List<StudentCheckInInfoVO> students,
        Timetable timetable, Integer courseTypeId, BatchBindClickerResultVO result) {
        log.info("开始处理课时扣减逻辑，学员数量: {}", students.size());

        for (StudentCheckInInfoVO student : students) {
            try {
                // 构建预校验请求参数
                StudentCheckInPreValidationDTO preValidationDTO = new StudentCheckInPreValidationDTO();
                preValidationDTO.setStudentId(student.getStudentId());
                preValidationDTO.setLessonNo(timetable.getLessonNo());
                preValidationDTO.setStoreId(timetable.getStoreId());
                preValidationDTO.setForceCheckIn(Integer.parseInt(YesNoEnum.NO.getCode()));

                // 执行课时扣减预校验
                R<ClassStudentErrorVO> validationResult = preValidateCheckIn(preValidationDTO);

                if (validationResult.isOk() && validationResult.getData() != null
                    && validationResult.getData().getCode() == 0) {
                    // 预校验通过，执行课时扣减
                    log.info("学员 {} 课时扣减预校验通过，开始执行课时扣减，课程类型: {}",
                        student.getStudentId(), courseTypeId);

                    // 使用指定的课程类型进行课时扣减
                    classTimeStudentManager.deductCourseHours(student.getStudentId(),
                        timetable.getId(), courseTypeId);

                    // 课时扣减成功后，更新学生出勤状态
                    classTimeStudentManager.updateStudentAttendanceStatus(student.getStudentId(),
                        timetable.getId());

                    result.setCourseHoursDeductSuccessCount(
                        result.getCourseHoursDeductSuccessCount() + 1);
                    log.info("学员 {} 课时扣减成功，课程类型: {}，出勤状态已更新",
                        student.getStudentId(), courseTypeId);

                } else {
                    // 预校验失败，记录异常信息
                    log.warn("学员 {} 课时扣减预校验失败: {}", student.getStudentId(),
                        validationResult.getData() != null ? validationResult.getData().getMsg()
                            : validationResult.getMsg());

                    BatchBindClickerResultVO.StudentCourseHoursErrorVO errorStudent = new BatchBindClickerResultVO.StudentCourseHoursErrorVO();
                    errorStudent.setStudentId(student.getStudentId());
                    errorStudent.setStudentName(student.getStudentName());
                    errorStudent.setStudentPhone(student.getStudentMobile());
                    errorStudent.setErrorInfo(validationResult.getData());

                    result.getCourseHoursErrorStudents().add(errorStudent);
                    result.setCourseHoursDeductErrorCount(
                        result.getCourseHoursDeductErrorCount() + 1);
                }

            } catch (Exception e) {
                log.error("学员 {} 课时扣减处理异常", student.getStudentId(), e);

                // 构建异常错误信息
                ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
                errorVO.setCode(-1);
                errorVO.setMsg("课时扣减处理异常: " + e.getMessage());

                BatchBindClickerResultVO.StudentCourseHoursErrorVO errorStudent = new BatchBindClickerResultVO.StudentCourseHoursErrorVO();
                errorStudent.setStudentId(student.getStudentId());
                errorStudent.setStudentName(student.getStudentName());
                errorStudent.setStudentPhone(student.getStudentMobile());
                errorStudent.setErrorInfo(errorVO);

                result.getCourseHoursErrorStudents().add(errorStudent);
                result.setCourseHoursDeductErrorCount(result.getCourseHoursDeductErrorCount() + 1);
            }
        }

        log.info("课时扣减处理完成 - 成功: {}, 异常: {}",
            result.getCourseHoursDeductSuccessCount(), result.getCourseHoursDeductErrorCount());
    }

    /**
     * 从课表信息中获取课程类型ID
     *
     * @param timetable 课表信息
     * @return 课程类型ID，如果获取失败返回null
     */
    private Integer getCourseTypeFromTimetable(Timetable timetable) {
        if (timetable == null || timetable.getCourseId() == null) {
            log.warn("课表信息或课程ID为空，无法获取课程类型");
            return null;
        }

        try {
            log.info("开始获取课程类型，courseId: {}", timetable.getCourseId());

            // 调用远程课程服务获取课程信息
            List<Long> courseIds = Collections.singletonList(timetable.getCourseId());
            R<Map<Long, CourseVO>> courseResult = remoteCourseService.getCourseMapByIdList(courseIds);

            if (!courseResult.isOk() || courseResult.getData() == null || courseResult.getData().isEmpty()) {
                log.warn("获取课程信息失败或为空，courseId: {}, result: {}", timetable.getCourseId(), courseResult);
                return null;
            }

            CourseVO courseVO = courseResult.getData().get(timetable.getCourseId());
            if (courseVO == null || courseVO.getCourseTypeId() == null) {
                log.warn("课程信息或课程类型ID为空，courseId: {}", timetable.getCourseId());
                return null;
            }

            // CourseVO中的courseTypeId是Long类型，需要转换为Integer
            Integer courseTypeId = courseVO.getCourseTypeId().intValue();
            log.info("成功获取课程类型，courseId: {}, courseTypeId: {}", timetable.getCourseId(), courseTypeId);

            return courseTypeId;

        } catch (Exception e) {
            log.error("获取课程类型异常，courseId: {}", timetable.getCourseId(), e);
            return null;
        }
    }

    @Override
    public List<Long> getStudentIdsByLessonNo(Long lessonNo) {
        if (Objects.isNull(lessonNo)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(BClassTimeStudent.class)
                .eq(BClassTimeStudent::getLessonNo, lessonNo)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode()))) // 过滤手动移除的学员
            .stream()
            .map(BClassTimeStudent::getStudentId)
            .toList();
    }

    /**
     * 插入只排课但未点进去考勤列表的课次学生插入班级学生
     *
     * @return void
     * <AUTHOR>
     * @date 2025/5/8 9:45
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveClassStudentIntoClassTimeStudent() {
        saveClassStudentIntoClassTimeStudent(null);
    }

    /**
     * 插入只排课但未点进去考勤列表的课次学生插入班级学生 支持指定课次号或处理所有已结束课次
     *
     * @param lessonNo 课次号，如果为null则处理所有已结束课次
     * @return void
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveClassStudentIntoClassTimeStudent(Long lessonNo) {
        // 现在课次学生的插入时机是当用户点击  课表签到考勤入口获取学生列表和考勤管理考勤列表才会触发插入，现在有一个问题是当用户只排了课，但是未点击进入考勤列表的时候，课次结束再进入就不会
        // 显示考勤学生，所以这个方法的逻辑是处理那些 课次已经结束但是课次学生表没有学生数据，筛选出来之后插入排课默认所属班级的学生

        List<Timetable> targetTimetables;

        if (lessonNo != null) {
            // 处理指定课次
            log.info("开始处理指定课次的学生插入, lessonNo: {}", lessonNo);
            Timetable timetable = timetableMapper.selectOne(
                Wrappers.<Timetable>lambdaQuery()
                    .eq(Timetable::getLessonNo, lessonNo)
                    .isNotNull(Timetable::getClassId)
                    .ne(Timetable::getClassId, 0L));

            if (timetable == null) {
                log.warn("未找到指定课次或课次没有关联班级, lessonNo: {}", lessonNo);
                return;
            }
            targetTimetables = Collections.singletonList(timetable);
        } else {
            // 处理所有已结束的课次（原有逻辑）
            log.info("开始处理已结束但未插入课次学生的课次");
            targetTimetables = timetableMapper.selectList(
                Wrappers.<Timetable>lambdaQuery()
                    .lt(Timetable::getClassEndDateTime, LocalDateTime.now())
                    .isNotNull(Timetable::getClassId)
                    .ne(Timetable::getClassId, 0L));

            if (CollUtil.isEmpty(targetTimetables)) {
                log.info("没有需要处理的已结束课次");
                return;
            }
        }

        log.info("找到需要处理的课次数量: {}", targetTimetables.size());

        // 2. 遍历课次，分类处理
        List<Timetable> noStudentTimetables = new ArrayList<>();        // 完全没有学生的课次
        List<Timetable> partialStudentTimetables = new ArrayList<>();   // 有学生但可能缺失原班级学生的课次

        for (Timetable timetable : targetTimetables) {
            // 查询该课次是否已有学生数据
            long count =
                this.count(
                    Wrappers.<BClassTimeStudent>lambdaQuery()
                        .eq(BClassTimeStudent::getLessonNo, timetable.getLessonNo())
                        .eq(BClassTimeStudent::getStoreId, timetable.getStoreId()));

            if (count == 0) {
                // 完全没有学生数据，需要处理
                noStudentTimetables.add(timetable);
            } else {
                // 有学生数据，但可能只有调课学生，原班级学生可能缺失
                partialStudentTimetables.add(timetable);
            }
        }

        log.info("课次分类结果 - 完全没有学生: {}, 有学生但可能缺失原班级学生: {}",
            noStudentTimetables.size(), partialStudentTimetables.size());

        if (lessonNo == null) {
            log.info(
                "定时任务将对已结束课程应用时间过滤规则，防止课程结束后新增/转班学生被错误插入");
        }

        // 3. 处理完全没有学生数据的课次（保持原有逻辑）
        if (CollUtil.isNotEmpty(noStudentTimetables)) {
            processEmptyTimetables(noStudentTimetables, lessonNo != null);
        }

        // 4. 处理可能缺失原班级学生的课次（新增逻辑）
        if (CollUtil.isNotEmpty(partialStudentTimetables)) {
            processPartialTimetables(partialStudentTimetables, lessonNo != null);
        }

        if (lessonNo != null) {
            log.info("指定课次学生数据处理完成, lessonNo: {}", lessonNo);
        } else {
            log.info("已结束课次学生数据处理完成");
        }
    }

    /**
     * 检查学生在课程中的有效性
     *
     * @param student         学生信息
     * @param courseEndTime   课程结束时间（可能为null）
     * @param applyTimeFilter 是否应用时间过滤（针对已结束课程）
     * @return 是否有效
     */
    private boolean isStudentValidForEndedCourse(StudentVO student, LocalDateTime courseEndTime,
        boolean applyTimeFilter) {
        // 检查学生基本状态
        boolean isValidStatus = Objects.equals(student.getClassStudentStatus(),
            ClassStudentEnum.FORMAL.getCode())
            && !Objects.equals(student.getStatus(), StudentStatusEnum.LEAVE.getCode());

        if (!isValidStatus) {
            log.debug("学生 {} 状态异常或已转出 - 班级状态: {}, 学生状态: {}",
                student.getName(), student.getClassStudentStatus(), student.getStatus());
            return false;
        }

        // 如果课程结束时间为null，说明课程时间信息不完整，不应用时间过滤
        if (courseEndTime == null) {
            log.debug("课程结束时间为null，不应用时间过滤，学生 {} 可以添加", student.getName());
            return true;
        }

        // 检查课程是否已结束，如果已结束则始终应用时间过滤
        boolean courseEnded = LocalDateTime.now().isAfter(courseEndTime);
        boolean shouldApplyTimeFilter = applyTimeFilter || courseEnded;

        // 应用时间过滤时才检查时间
        if (shouldApplyTimeFilter) {
            // 检查学生入班时间是否在课程结束之前
            if (student.getInClassTime() != null) {
                if (student.getInClassTime().isAfter(courseEndTime)) {
                    log.debug("学生 {} 入班时间 {} 晚于课程结束时间 {}，不添加到已结束课程",
                        student.getName(), student.getInClassTime(), courseEndTime);
                    return false;
                }
            }

            // 对于转班学生，额外检查转班时间
            if (Objects.equals(student.getChangeClass(),
                Integer.valueOf(YesNoEnum.YES.getCode()))) {
                // 转班学生的入班时间应该在课程结束之前
                if (student.getInClassTime() != null && student.getInClassTime()
                    .isAfter(courseEndTime)) {
                    log.debug("转班学生 {} 转班时间 {} 晚于课程结束时间 {}，不添加到已结束课程",
                        student.getName(), student.getInClassTime(), courseEndTime);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 处理完全没有学生数据的课次（原有逻辑）
     *
     * @param timetables       课次列表
     * @param isSpecificLesson 是否为指定课次处理
     */
    private void processEmptyTimetables(List<Timetable> timetables, boolean isSpecificLesson) {
        log.info("开始处理完全没有学生数据的课次，数量: {}", timetables.size());

        for (Timetable timetable : timetables) {
            try {
                // 获取班级学生列表
                R<List<StudentVO>> classStudentsResult =
                    remoteStudentService.getListByClassId(timetable.getClassId().intValue());

                if (!classStudentsResult.isOk() || CollUtil.isEmpty(
                    classStudentsResult.getData())) {
                    log.warn("班级 {} 没有学生数据，跳过处理课次 {}", timetable.getClassId(),
                        timetable.getLessonNo());
                    continue;
                }

                // 过滤出在班且状态正常的学生，并检查时间有效性
                // 对于指定课次处理，不应用时间过滤；对于定时任务处理已结束课次，应用时间过滤
                List<StudentVO> validStudents = classStudentsResult.getData().stream()
                    .filter(student -> isStudentValidForEndedCourse(student,
                        timetable.getClassEndDateTime(), !isSpecificLesson))
                    .toList();

                // 统计过滤原因
                int totalStudents = classStudentsResult.getData().size();
                int statusFilteredCount = (int) classStudentsResult.getData().stream()
                    .filter(student -> !Objects.equals(student.getClassStudentStatus(),
                        ClassStudentEnum.FORMAL.getCode())
                        || Objects.equals(student.getStatus(), StudentStatusEnum.LEAVE.getCode()))
                    .count();
                int timeFilteredCount = (int) classStudentsResult.getData().stream()
                    .filter(student -> Objects.equals(student.getClassStudentStatus(),
                        ClassStudentEnum.FORMAL.getCode())
                        && !Objects.equals(student.getStatus(), StudentStatusEnum.LEAVE.getCode()))
                    .filter(student -> student.getCreateTime() != null
                        && timetable.getClassEndDateTime() != null
                        && student.getCreateTime().isAfter(timetable.getClassEndDateTime()))
                    .count();

                log.info(
                    "班级 {} 学生统计 - 总数: {}, 状态过滤: {}, 时间过滤: {}, 有效学生: {}, 课次: {}",
                    timetable.getClassId(), totalStudents, statusFilteredCount, timeFilteredCount,
                    validStudents.size(), timetable.getLessonNo());

                // 批量构建课次学生数据
                List<BClassTimeStudent> classTimeStudents = new ArrayList<>();

                for (StudentVO student : validStudents) {
                    BClassTimeStudent classTimeStudent = new BClassTimeStudent();
                    classTimeStudent.setStoreId(timetable.getStoreId());
                    classTimeStudent.setStudentId(student.getUserId());
                    classTimeStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_1.code); // 1-班级学生
                    classTimeStudent.setLessonNo(timetable.getLessonNo());
                    classTimeStudent.setCheckInStatus(
                        CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 0-未出勤(缺勤)
                    classTimeStudent.setAdjustStatus(
                        Integer.valueOf(YesNoEnum.NO.getCode())); // 0-未调出
                    classTimeStudent.setIsRegularStudents(
                        student.getIsRegularStudents()); // 设置是否是正式学员
                    classTimeStudents.add(classTimeStudent);
                }

                // 如果是补课类型，还需要添加原课次的临时学员
                if (Objects.equals(timetable.getCourseType(),
                    CourseTypeEnum.COURSE_TYPE_ENUM_3.code)) {
                    log.info("检测到补课类型课程，添加原课次的临时学员, lessonNo: {}",
                        timetable.getLessonNo());
                    List<StudentVO> temporaryStudents = getTemporaryStudentsFromOriginalLesson(
                        timetable, timetable.getStoreId());
                    if (CollUtil.isNotEmpty(temporaryStudents)) {
                        log.info("找到原课次临时学员数量: {}", temporaryStudents.size());

                        // 创建临时学员记录
                        for (StudentVO student : temporaryStudents) {
                            BClassTimeStudent temporaryRecord = new BClassTimeStudent();
                            temporaryRecord.setStoreId(timetable.getStoreId());
                            temporaryRecord.setStudentId(student.getUserId());
                            temporaryRecord.setStudentType(
                                StudentTypeEnum.STUDENT_TYPE_4.code); // 4-临加学员
                            temporaryRecord.setLessonNo(timetable.getLessonNo());
                            temporaryRecord.setCheckInStatus(
                                CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 0-未出勤
                            temporaryRecord.setAdjustStatus(
                                Integer.valueOf(YesNoEnum.NO.getCode())); // 0-未调出
                            temporaryRecord.setIsRegularStudents(student.getIsRegularStudents());
                            classTimeStudents.add(temporaryRecord);
                        }
                        log.info("补课课程添加原课次临时学员数量: {}", temporaryStudents.size());
                    }
                }

                // 批量保存课次学生数据
                if (CollUtil.isNotEmpty(classTimeStudents)) {
                    this.saveBatch(classTimeStudents);
                    log.info("成功为课次 {} 添加 {} 名有效学生", timetable.getLessonNo(),
                        classTimeStudents.size());
                } else {
                    log.info("课次 {} 没有需要添加的有效学生", timetable.getLessonNo());
                }
            } catch (Exception e) {
                log.error("处理课次 {} 的学生数据时发生错误", timetable.getLessonNo(), e);
            }
        }
    }

    /**
     * 处理有学生但可能缺失原班级学生的课次（新增逻辑）
     *
     * @param timetables       课次列表
     * @param isSpecificLesson 是否为指定课次处理
     */
    private void processPartialTimetables(List<Timetable> timetables, boolean isSpecificLesson) {
        log.info("开始处理可能缺失原班级学生的课次，数量: {}", timetables.size());

        for (Timetable timetable : timetables) {
            try {
                supplementMissingClassStudents(timetable, isSpecificLesson);
            } catch (Exception e) {
                log.error("处理课次 {} 缺失学生时发生错误", timetable.getLessonNo(), e);
            }
        }
    }

    /**
     * 检查是否需要补充原班级学生
     *
     * @param timetable        课表信息
     * @param existingStudents 现有学生列表
     * @return 是否需要补充
     */
    private boolean needSupplementClassStudents(Timetable timetable,
        List<BClassTimeStudent> existingStudents) {
        try {
            // 获取班级学生列表
            R<List<StudentVO>> classStudentsResult =
                remoteStudentService.getListByClassId(timetable.getClassId().intValue());

            if (!classStudentsResult.isOk() || CollUtil.isEmpty(classStudentsResult.getData())) {
                log.debug("班级 {} 没有学生数据，无需补充", timetable.getClassId());
                return false;
            }

            // 获取现有学生ID集合
            Set<Long> existingStudentIds = existingStudents.stream()
                .map(BClassTimeStudent::getStudentId)
                .collect(Collectors.toSet());

            // 获取调课调出的学生ID集合
            List<TimetableChange> timetableChanges = timetableChangeMapper.selectList(
                Wrappers.<TimetableChange>lambdaQuery()
                    .eq(TimetableChange::getSourceLessonNo, timetable.getLessonNo())
                    .eq(TimetableChange::getSourceStoreId, timetable.getStoreId()));

            Set<Long> adjustedOutStudents = timetableChanges.stream()
                .map(TimetableChange::getStudentId)
                .collect(Collectors.toSet());

            // 检查班级学生中是否有缺失的
            List<StudentVO> classStudents = classStudentsResult.getData().stream()
                .filter(student -> isStudentValidForEndedCourse(student,
                    timetable.getClassEndDateTime(), false))
                .toList();

            for (StudentVO student : classStudents) {
                Long studentId = student.getUserId();

                // 如果班级学生不在现有学生中，且没有被调出，则需要补充
                if (!existingStudentIds.contains(studentId) && !adjustedOutStudents.contains(
                    studentId)) {
                    log.debug("发现缺失的原班级学生: studentId={}, lessonNo={}", studentId,
                        timetable.getLessonNo());
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查是否需要补充原班级学生时发生错误, lessonNo: {}", timetable.getLessonNo(),
                e);
            return false;
        }
    }

    /**
     * 补充缺失的原班级学生
     *
     * @param timetable        课表信息
     * @param isSpecificLesson 是否为指定课次处理
     */
    private void supplementMissingClassStudents(Timetable timetable, boolean isSpecificLesson) {
        Long lessonNo = timetable.getLessonNo();
        Long storeId = timetable.getStoreId();

        log.debug("检查课次 {} 是否缺失原班级学生", lessonNo);

        // 1. 获取课次现有学生ID集合（过滤手动移除的学员）
        List<Long> existingStudentIds = this.list(
                Wrappers.<BClassTimeStudent>lambdaQuery()
                    .eq(BClassTimeStudent::getLessonNo, lessonNo)
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .ne(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode())) // 过滤手动移除的学员
                    .select(BClassTimeStudent::getStudentId))
            .stream()
            .map(BClassTimeStudent::getStudentId)
            .toList();

        // 2. 获取班级学生列表
        R<List<StudentVO>> classStudentsResult =
            remoteStudentService.getListByClassId(timetable.getClassId().intValue());

        if (!classStudentsResult.isOk() || CollUtil.isEmpty(classStudentsResult.getData())) {
            log.debug("班级 {} 没有学生数据，跳过课次 {}", timetable.getClassId(), lessonNo);
            return;
        }

        List<StudentVO> classStudents = classStudentsResult.getData();

        // 3. 检查是否有调课记录影响学生归属
        List<TimetableChange> timetableChanges = timetableChangeMapper.selectList(
            Wrappers.<TimetableChange>lambdaQuery()
                .and(wrapper -> wrapper
                    .eq(TimetableChange::getSourceLessonNo, lessonNo)
                    .or()
                    .eq(TimetableChange::getTargetLessonNo, lessonNo)));

        // 4. 分析调课对学生的影响
        Set<Long> adjustedOutStudents = new HashSet<>();  // 被调出的学生
        Set<Long> adjustedInStudents = new HashSet<>();   // 被调入的学生

        for (TimetableChange change : timetableChanges) {
            if (lessonNo.equals(change.getSourceLessonNo())) {
                // 从当前课次调出的学生
                adjustedOutStudents.add(change.getStudentId());
            }
            if (lessonNo.equals(change.getTargetLessonNo())) {
                // 调入当前课次的学生
                adjustedInStudents.add(change.getStudentId());
            }
        }

        log.debug("课次 {} 调课情况 - 调出学生: {}, 调入学生: {}",
            lessonNo, adjustedOutStudents.size(), adjustedInStudents.size());

        // 5. 找出需要补充的原班级学生
        List<BClassTimeStudent> missingStudents = new ArrayList<>();

        for (StudentVO student : classStudents) {
            Long studentId = student.getUserId();

            // 跳过已存在的学生
            if (existingStudentIds.contains(studentId)) {
                continue;
            }

            // 跳过已被调出的学生
            if (adjustedOutStudents.contains(studentId)) {
                log.debug("学生 {} 已被调出课次 {}，不添加", studentId, lessonNo);
                continue;
            }

            // 检查学生状态和时间有效性
            // 对于指定课次处理，不应用时间过滤；对于定时任务处理已结束课次，应用时间过滤
            boolean isValidStudent = isStudentValidForEndedCourse(student,
                timetable.getClassEndDateTime(), !isSpecificLesson);

            if (isValidStudent) {
                log.debug("发现缺失的原班级学生: studentId={}, lessonNo={}", studentId, lessonNo);

                BClassTimeStudent missingStudent = new BClassTimeStudent();
                missingStudent.setStoreId(storeId);
                missingStudent.setStudentId(studentId);
                missingStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_1.code); // 1-班级学生
                missingStudent.setLessonNo(lessonNo);
                missingStudent.setCheckInStatus(
                    CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 0-未出勤(缺勤)
                missingStudent.setAdjustStatus(Integer.valueOf(YesNoEnum.NO.getCode())); // 0-未调出
                missingStudent.setIsRegularStudents(student.getIsRegularStudents());
                missingStudents.add(missingStudent);
            }
        }

        // 6. 统计处理结果
        int totalClassStudents = classStudents.size();
        int existingStudents = existingStudentIds.size();
        int adjustedOutCount = adjustedOutStudents.size();
        int timeFilteredCount = 0;
        int statusFilteredCount = 0;

        for (StudentVO student : classStudents) {
            if (!existingStudentIds.contains(student.getUserId()) && !adjustedOutStudents.contains(
                student.getUserId())) {
                boolean hasValidStatus = Objects.equals(student.getClassStudentStatus(),
                    ClassStudentEnum.FORMAL.getCode())
                    && !Objects.equals(student.getStatus(), StudentStatusEnum.LEAVE.getCode());
                if (!hasValidStatus) {
                    statusFilteredCount++;
                } else if (student.getCreateTime() != null
                    && timetable.getClassEndDateTime() != null
                    && student.getCreateTime().isAfter(timetable.getClassEndDateTime())) {
                    timeFilteredCount++;
                }
            }
        }

        log.info(
            "课次 {} 学生补充统计 - 班级总数: {}, 已存在: {}, 已调出: {}, 状态过滤: {}, 时间过滤: {}, 补充数量: {}",
            lessonNo, totalClassStudents, existingStudents, adjustedOutCount,
            statusFilteredCount, timeFilteredCount, missingStudents.size());

        // 7. 如果是补课类型，还需要检查是否缺失原课次的临时学员
        if (Objects.equals(timetable.getCourseType(), CourseTypeEnum.COURSE_TYPE_ENUM_3.code)) {
            log.info("检测到补课类型课程，检查是否缺失原课次的临时学员, lessonNo: {}", lessonNo);
            List<StudentVO> temporaryStudents = getTemporaryStudentsFromOriginalLesson(timetable,
                storeId);
            if (CollUtil.isNotEmpty(temporaryStudents)) {
                log.info("找到原课次临时学员数量: {}", temporaryStudents.size());

                // 检查这些临时学员是否已经存在
                for (StudentVO student : temporaryStudents) {
                    if (!existingStudentIds.contains(student.getUserId())) {
                        log.debug("发现缺失的原课次临时学员: studentId={}, lessonNo={}",
                            student.getUserId(), lessonNo);

                        BClassTimeStudent temporaryRecord = new BClassTimeStudent();
                        temporaryRecord.setStoreId(storeId);
                        temporaryRecord.setStudentId(student.getUserId());
                        temporaryRecord.setStudentType(
                            StudentTypeEnum.STUDENT_TYPE_4.code); // 4-临加学员
                        temporaryRecord.setLessonNo(lessonNo);
                        temporaryRecord.setCheckInStatus(
                            CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 0-未出勤
                        temporaryRecord.setAdjustStatus(
                            Integer.valueOf(YesNoEnum.NO.getCode())); // 0-未调出
                        temporaryRecord.setIsRegularStudents(student.getIsRegularStudents());
                        missingStudents.add(temporaryRecord);
                    }
                }
                log.info("补课课程需要补充的原课次临时学员数量: {}",
                    temporaryStudents.stream()
                        .filter(s -> !existingStudentIds.contains(s.getUserId())).count());
            }
        }

        // 8. 批量添加缺失的学生
        if (CollUtil.isNotEmpty(missingStudents)) {
            this.saveBatch(missingStudents);
            log.info("为课次 {} 补充了 {} 名缺失的学生（包括原班级学生和原课次临时学员）", lessonNo,
                missingStudents.size());
        } else {
            log.debug("课次 {} 没有发现缺失的学生", lessonNo);
        }
    }

    /**
     * 获取课程信息
     *
     * @param courseId 课程ID
     * @return 课程信息
     */
    @Override
    public CourseVO getCourseInfo(Long courseId) {
        try {
            CourseDTO courseDTO = new CourseDTO();
            courseDTO.setCourseIdList(Collections.singletonList(courseId.intValue()));
            R<List<CourseVO>> courseResult = remoteCourseService.getCourseListByIds(courseDTO);
            if (courseResult.isOk() && CollectionUtils.isNotEmpty(courseResult.getData())) {
                return courseResult.getData().stream().findFirst().orElse(null);
            }
        } catch (Exception e) {
            log.error("获取课程信息失败, courseId: {}", courseId, e);
        }
        return null;
    }

    /**
     * 根据课次号筛选是否存在签到的学生
     *
     * @param timetableLessons
     * @param storeId
     * @return boolean
     * <AUTHOR>
     * @date 2025/5/13 16:17
     */
    @Override
    public boolean checkStudentsExistByLeeonNos(List<Long> timetableLessons, Long storeId) {
        if (CollectionUtils.isEmpty(timetableLessons)) {
            return false;
        }
        // 查询是否有学生签到记录
        int count = Math.toIntExact(
            baseMapper.selectCount(Wrappers.lambdaQuery(BClassTimeStudent.class)
                .in(BClassTimeStudent::getLessonNo, timetableLessons)
                .eq(storeId != null, BClassTimeStudent::getStoreId, storeId)
                .eq(BClassTimeStudent::getCheckInStatus,
                    CheckInStatusEnum.CHECK_IN_STATUS_1.code)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode()))));  // 过滤手动移除的学员

        return count > 0;
    }

    /**
     * 检查未结束课节中是否存在已签到的学生 只检查课程结束时间晚于当前时间的课节
     *
     * @param timetableLessons 课次号列表
     * @param storeId          门店ID
     * @return boolean 如果未结束的课节中存在已签到学生返回true，否则返回false
     */
    @Override
    public boolean checkStudentsExistByUnfinishedLessons(List<Long> timetableLessons,
        Long storeId) {
        if (CollectionUtils.isEmpty(timetableLessons)) {
            log.info("课次号列表为空，返回false");
            return false;
        }

        log.info("开始检查未结束课节的学生签到情况，课次数量: {}", timetableLessons.size());

        // 1. 先查询课表信息，过滤出未结束的课节
        List<Timetable> timetables = timetableMapper.selectList(
            Wrappers.lambdaQuery(Timetable.class)
                .in(Timetable::getLessonNo, timetableLessons)
                .eq(storeId != null, Timetable::getStoreId, storeId)
        );

        if (CollectionUtils.isEmpty(timetables)) {
            log.info("未查询到相关课表信息");
            return false;
        }

        // 2. 过滤出未结束的课节（课程结束时间晚于当前时间）
        LocalDateTime now = LocalDateTime.now();
        List<Long> unfinishedLessonNos = timetables.stream()
            .filter(timetable -> Objects.nonNull(timetable.getClassEndDateTime()) &&
                timetable.getClassEndDateTime().isAfter(now))
            .map(Timetable::getLessonNo)
            .collect(Collectors.toList());

        log.info("过滤后的未结束课节数量: {}，原始课节数量: {}", unfinishedLessonNos.size(),
            timetables.size());

        if (CollectionUtils.isEmpty(unfinishedLessonNos)) {
            log.info("所有课节均已结束，允许修改班级");
            return false;
        }

        // 3. 检查未结束课节中是否有学生签到记录
        int count = Math.toIntExact(
            baseMapper.selectCount(Wrappers.lambdaQuery(BClassTimeStudent.class)
                .in(BClassTimeStudent::getLessonNo, unfinishedLessonNos)
                .eq(storeId != null, BClassTimeStudent::getStoreId, storeId)
                .eq(BClassTimeStudent::getCheckInStatus,
                    CheckInStatusEnum.CHECK_IN_STATUS_1.code)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode())))); // 过滤手动移除的学员

        log.info("未结束课节中已签到学生数量: {}", count);
        return count > 0;
    }

    @Override
    public Map<Long, Long> getLessonStudentCount(List<Long> lessonNoList, Long storeId) {
        return this.list(
                Wrappers.lambdaQuery(BClassTimeStudent.class)
                    .in(BClassTimeStudent::getLessonNo, lessonNoList)
                    .eq(BClassTimeStudent::getCheckInStatus,
                        CheckInStatusEnum.CHECK_IN_STATUS_1.code)
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .ne(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode()))) // 过滤手动移除的学员
            .stream()
            .collect(Collectors.groupingBy(
                BClassTimeStudent::getLessonNo,
                Collectors.counting()
            ));
    }

    /**
     * 学生签到预校验
     *
     * @param preValidationDTO 预校验请求参数
     * @return 预校验结果
     */
    @Override
    public R<ClassStudentErrorVO> preValidateCheckIn(
        StudentCheckInPreValidationDTO preValidationDTO) {
        log.info("开始执行学生签到预校验, 参数: {}", JSON.toJSONString(preValidationDTO));

        Long studentId = preValidationDTO.getStudentId();
        Long lessonNo = preValidationDTO.getLessonNo();
        Long storeId = StoreContextHolder.getStoreId();

        try {
            // 1. 获取课表信息
            Timetable timetable = getTimetableInfo(lessonNo, storeId);
            Long courseId = timetable.getCourseId();
            log.info("获取课表信息成功, lessonNo: {}, courseId: {}, classDate: {}",
                lessonNo, courseId, timetable.getClassDate());

            // 2. 通过课程ID获取课程信息，获取课程类型ID
            R<Map<Long, CourseVO>> courseResult = remoteCourseService.getCourseMapByIdList(
                List.of(courseId));
            if (!courseResult.isOk() || CollUtil.isEmpty(courseResult.getData())) {
                log.error("预检学生剩余课次获取课程信息失败, courseId: {}, result: {}", courseId,
                    courseResult);
                return R.failed("获取课程信息失败");
            }
            CourseVO courseVO = courseResult.getData().get(courseId);
            if (courseVO == null || courseVO.getCourseTypeId() == null) {
                log.error("预检学生剩余课次课程信息不完整, courseId: {}, courseVO: {}", courseId,
                    courseVO);
                return R.failed("课程信息不完整，无法获取课程类型");
            }
            Integer currentCourseType = courseVO.getCourseTypeId().intValue();
            log.info("获取课程信息成功, courseId: {}, courseName: {}, courseTypeId: {}",
                courseId, courseVO.getCourseName(), currentCourseType);

            // 3. 获取学生信息和课程类型报名信息
            R<List<StudentVO>> studentResult = remoteStudentService.getStudentListByIds(
                List.of(studentId));
            if (!studentResult.isOk() || CollUtil.isEmpty(studentResult.getData())) {
                log.error("预检学生剩余课次获取学生信息失败, studentId: {}, result: {}", studentId,
                    studentResult);
                return R.failed("获取学生信息失败");
            }
            StudentVO studentVO = studentResult.getData().get(0);
            List<CourseHoursStudent> studentCourseTypes = studentVO.getCourseHoursList();
            log.info(
                "获取学生信息成功, studentId: {}, studentName: {}, phone: {}, courseTypesCount: {}",
                studentId, studentVO.getName(), studentVO.getPhone(),
                CollUtil.isEmpty(studentCourseTypes) ? 0 : studentCourseTypes.size());

            // 4. 执行校验逻辑
            log.info("开始执行校验逻辑, studentId: {}, currentCourseType: {}", studentId,
                currentCourseType);
            return performValidation(preValidationDTO, studentVO, currentCourseType,
                studentCourseTypes, courseVO.getCourseName(), timetable);

        } catch (Exception e) {
            log.error("学生签到预校验异常, studentId: {}, lessonNo: {}", studentId, lessonNo, e);
            return R.failed("签到预校验失败: " + e.getMessage());
        }
    }

    /**
     * 执行签到校验逻辑
     */
    private R<ClassStudentErrorVO> performValidation(
        StudentCheckInPreValidationDTO preValidationDTO,
        StudentVO studentVO,
        Integer currentCourseType,
        List<CourseHoursStudent> studentCourseTypes,
        String currentCourseName,
        Timetable timetable) {

        log.info(
            "执行签到校验逻辑, studentId: {}, studentName: {}, currentCourseType: {}, currentCourseName: {}",
            studentVO.getUserId(), studentVO.getName(), currentCourseType, currentCourseName);


        // 检查试听学员剩余课次是否为0
        if (Objects.equals(studentVO.getIsRegularStudents(), StudentRegularEnum.TRIAL.getCode())) {
            Integer remainingHours = studentVO.getCourseHours();
            log.info("检查试听学员剩余课次, studentId: {}, studentName: {}, remainingHours: {}",
                studentVO.getUserId(), studentVO.getName(), remainingHours);

            if (remainingHours == null || remainingHours == 0) {
                log.warn("试听学员剩余课次为0，无法进行考勤, studentId: {}, studentName: {}",
                    studentVO.getUserId(), studentVO.getName());
                ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
                errorVO.setCode(ClassStudentErrorEnum.TRIAL_STUDENT_ZERO_HOURS_ERROR.getCode());
                errorVO.setMsg(ClassStudentErrorEnum.TRIAL_STUDENT_ZERO_HOURS_ERROR.getDesc());
                errorVO.setCurrentCourseTypeId(currentCourseType);
                log.info("返回试听学员剩余课次为0错误, errorCode: {}", errorVO.getCode());
                return R.ok(errorVO);
            }
        }

        // 如果学生没有任何课程类型报名信息，直接返回不允许签到
        if (CollUtil.isEmpty(studentCourseTypes)) {
            log.warn("学生未报名任何课程类型, studentId: {}, studentName: {}",
                studentVO.getUserId(), studentVO.getName());
            ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
            errorVO.setCode(ClassStudentErrorEnum.NO_COURSE_ENROLLMENT_ERROR.getCode());
            errorVO.setMsg(String.format(ClassStudentErrorEnum.NO_COURSE_ENROLLMENT_ERROR.getDesc(),
                studentVO.getName(), studentVO.getPhone()));
            errorVO.setCurrentCourseTypeId(currentCourseType); // 设置当前课程类型ID
            log.info("返回未报名任何课程类型错误, errorCode: {}", errorVO.getCode());
            return R.ok(errorVO);
        }

        // 打印学生所有课程类型报名信息
        log.info("学生课程类型报名信息: {}", studentCourseTypes.stream()
            .map(ct -> String.format("courseType=%d, remainingHours=%d, formal=%d, gift=%d",
                ct.getCourseType(), ct.getCourseHours(), ct.getFormal(), ct.getGift()))
            .collect(Collectors.joining("; ")));

        // 查找当前课程类型的报名信息
        CourseHoursStudent currentCourseTypeEnrollment = studentCourseTypes.stream()
            .filter(enrollment -> enrollment.getCourseType().equals(currentCourseType))
            .findFirst()
            .orElse(null);

        // 获取其他课程类型的报名信息（剩余课次大于0的）
        List<CourseHoursStudent> otherCourseTypes = studentCourseTypes.stream()
            .filter(enrollment -> !enrollment.getCourseType().equals(currentCourseType))
            .filter(enrollment -> enrollment.getCourseHours() != null
                && enrollment.getCourseHours() > 0)
            .collect(Collectors.toList());

        log.info("其他可用课程类型数量: {}", otherCourseTypes.size());

        // 情况1: 学生未报名当前课程类型
        if (currentCourseTypeEnrollment == null) {
            log.warn("学生未报名当前课程类型, studentId: {}, currentCourseType: {}",
                studentVO.getUserId(), currentCourseType);
            return handleUnenrolledCourse(studentVO, currentCourseType, currentCourseName,
                otherCourseTypes);
        }

        // 情况2: 学生已报名当前课程类型
        Integer currentRemainingSessions = currentCourseTypeEnrollment.getCourseHours();
        log.info("学生当前课程类型剩余课次: {}, courseType: {}", currentRemainingSessions,
            currentCourseType);

        if (currentRemainingSessions != null && currentRemainingSessions > 0) {
            // 检查是否存在引流课且当前课程不是引流课的情况
            R<ClassStudentErrorVO> trialCourseCheckResult = checkTrialCourseConflict(
                studentVO, currentCourseType, currentCourseName, studentCourseTypes);
            if (trialCourseCheckResult != null) {
                return trialCourseCheckResult;
            }

            // 可以正常签到
            log.info(
                "学生可以正常签到, studentId: {}, currentCourseType: {}, remainingSessions: {}",
                studentVO.getUserId(), currentCourseType, currentRemainingSessions);
            ClassStudentErrorVO successVO = new ClassStudentErrorVO();
            successVO.setCurrentCourseTypeId(currentCourseType); // 设置当前课程类型ID
            return R.ok(successVO);
        } else {
            // 当前课程类型剩余课次为0，需要进一步校验
            log.warn("学生当前课程类型剩余课次为0, studentId: {}, currentCourseType: {}",
                studentVO.getUserId(), currentCourseType);
            return handleZeroRemainingSessions(studentVO, currentCourseType, currentCourseName,
                otherCourseTypes);
        }
    }

    /**
     * 检查引流课冲突 当学员有引流课课时剩余且当前课程不是引流课时，提示选择扣减课程类型
     */
    private R<ClassStudentErrorVO> checkTrialCourseConflict(
        StudentVO studentVO,
        Integer currentCourseType,
        String currentCourseName,
        List<CourseHoursStudent> studentCourseTypes) {

        log.info("检查引流课冲突, studentId: {}, currentCourseType: {}",
            studentVO.getUserId(), currentCourseType);

        // 如果当前课程就是引流课，无需检查
        if (Objects.equals(StoreConstant.TRIAL_COURSE_TYPE, currentCourseType)) {
            log.info("当前课程是引流课，无需检查冲突, studentId: {}, currentCourseType: {}",
                studentVO.getUserId(), currentCourseType);
            return null;
        }

        // 查找引流课课时记录
        CourseHoursStudent trialCourseHours = studentCourseTypes.stream()
            .filter(enrollment -> Objects.equals(StoreConstant.TRIAL_COURSE_TYPE,
                enrollment.getCourseType()))
            .filter(enrollment -> enrollment.getCourseHours() != null
                && enrollment.getCourseHours() > 0)
            .findFirst()
            .orElse(null);

        // 如果没有引流课课时剩余，无需检查
        if (trialCourseHours == null) {
            log.info("学员没有引流课课时剩余，无需检查冲突, studentId: {}", studentVO.getUserId());
            return null;
        }

        log.warn(
            "学员存在引流课课时剩余且当前课程不是引流课, studentId: {}, currentCourseType: {}, trialRemaining: {}",
            studentVO.getUserId(), currentCourseType, trialCourseHours.getCourseHours());

        // 获取所有有剩余课时的课程类型（包括引流课）
        List<CourseHoursStudent> availableCourseTypes = studentCourseTypes.stream()
            .filter(enrollment -> enrollment.getCourseHours() != null
                && enrollment.getCourseHours() > 0)
            .sorted((a, b) -> {
                // 引流课类型排在第一位
                boolean aIsTrial = Objects.equals(a.getCourseType(),
                    StoreConstant.TRIAL_COURSE_TYPE);
                boolean bIsTrial = Objects.equals(b.getCourseType(),
                    StoreConstant.TRIAL_COURSE_TYPE);
                if (aIsTrial && !bIsTrial) {
                    return -1; // a排在前面
                } else if (!aIsTrial && bIsTrial) {
                    return 1;  // b排在前面
                } else {
                    return 0;  // 保持原有顺序
                }
            })
            .collect(Collectors.toList());

        log.info("课程类型排序完成，引流课类型优先, availableCourseTypesCount: {}",
            availableCourseTypes.size());

        ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
        errorVO.setCode(ClassStudentErrorEnum.TRIAL_COURSE_SELECT_TYPE_ERROR.getCode());
        errorVO.setMsg(String.format(ClassStudentErrorEnum.TRIAL_COURSE_SELECT_TYPE_ERROR.getDesc(),
            studentVO.getName(), studentVO.getPhone()));
        errorVO.setCurrentCourseTypeId(currentCourseType);
        errorVO.setOtherCourseTypes(buildCourseTypeInfoList(availableCourseTypes));

        log.info("返回引流课选择类型错误, errorCode: {}, availableCourseTypesCount: {}",
            errorVO.getCode(), availableCourseTypes.size());

        return R.ok(errorVO);
    }

    /**
     * 处理未报名课程的情况
     */
    private R<ClassStudentErrorVO> handleUnenrolledCourse(
        StudentVO studentVO,
        Integer currentCourseType,
        String currentCourseName,
        List<CourseHoursStudent> otherCourseTypes) {

        log.info(
            "处理未报名课程情况, studentId: {}, currentCourseType: {}, otherCourseTypesCount: {}",
            studentVO.getUserId(), currentCourseType, otherCourseTypes.size());

        ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
        errorVO.setCurrentCourseTypeId(currentCourseType); // 设置当前课程类型ID

        if (CollUtil.isEmpty(otherCourseTypes)) {
            // 没有其他课程类型报名
            log.warn("学生未报名当前课程且无其他可用课程类型, studentId: {}, currentCourseType: {}",
                studentVO.getUserId(), currentCourseType);
            errorVO.setCode(ClassStudentErrorEnum.UNENROLLED_COURSE_NO_OTHER_ERROR.getCode());
            errorVO.setMsg(
                String.format(ClassStudentErrorEnum.UNENROLLED_COURSE_NO_OTHER_ERROR.getDesc(),
                    studentVO.getName(), studentVO.getPhone(), currentCourseName));
            log.info("返回未报名课程且无其他可用类型错误, errorCode: {}", errorVO.getCode());
        } else {
            // 有其他课程类型可以扣减
            log.info(
                "学生未报名当前课程但有其他可用课程类型, studentId: {}, currentCourseType: {}, otherTypes: {}",
                studentVO.getUserId(), currentCourseType,
                otherCourseTypes.stream().map(ct -> ct.getCourseType().toString())
                    .collect(Collectors.joining(",")));
            errorVO.setCode(ClassStudentErrorEnum.UNENROLLED_COURSE_SELECT_OTHER_ERROR.getCode());
            errorVO.setMsg(
                String.format(ClassStudentErrorEnum.UNENROLLED_COURSE_SELECT_OTHER_ERROR.getDesc(),
                    studentVO.getName(), studentVO.getPhone(), currentCourseName));
            errorVO.setOtherCourseTypes(buildCourseTypeInfoList(otherCourseTypes));
            log.info("返回未报名课程选择其他类型错误, errorCode: {}, otherCourseTypesCount: {}",
                errorVO.getCode(), errorVO.getOtherCourseTypes().size());
        }

        return R.ok(errorVO);
    }

    /**
     * 处理剩余课次为0的情况
     */
    private R<ClassStudentErrorVO> handleZeroRemainingSessions(
        StudentVO studentVO,
        Integer currentCourseType,
        String currentCourseName,
        List<CourseHoursStudent> otherCourseTypes) {

        log.info(
            "处理剩余课次为0情况, studentId: {}, currentCourseType: {}, otherCourseTypesCount: {}",
            studentVO.getUserId(), currentCourseType, otherCourseTypes.size());

        ClassStudentErrorVO errorVO = new ClassStudentErrorVO();
        errorVO.setCurrentCourseTypeId(currentCourseType); // 设置当前课程类型ID

        if (CollUtil.isEmpty(otherCourseTypes)) {
            // 没有其他课程类型或其他课程类型剩余课次都为0
            log.warn(
                "学生当前课程类型剩余课次为0且无其他可用课程类型, studentId: {}, currentCourseType: {}",
                studentVO.getUserId(), currentCourseType);
            errorVO.setCode(ClassStudentErrorEnum.ZERO_SESSIONS_FORCE_CHECKIN_ERROR.getCode());
            errorVO.setMsg(
                String.format(ClassStudentErrorEnum.ZERO_SESSIONS_FORCE_CHECKIN_ERROR.getDesc(),
                    studentVO.getName(), studentVO.getPhone()));
            log.info("返回剩余课次为0强制签到错误, errorCode: {}", errorVO.getCode());
        } else {
            // 有其他课程类型可以扣减
            log.info(
                "学生当前课程类型剩余课次为0但有其他可用课程类型, studentId: {}, currentCourseType: {}, otherTypes: {}",
                studentVO.getUserId(), currentCourseType,
                otherCourseTypes.stream().map(ct -> ct.getCourseType().toString())
                    .collect(Collectors.joining(",")));
            errorVO.setCode(ClassStudentErrorEnum.ZERO_SESSIONS_SELECT_OTHER_ERROR.getCode());
            errorVO.setMsg(
                String.format(ClassStudentErrorEnum.ZERO_SESSIONS_SELECT_OTHER_ERROR.getDesc(),
                    studentVO.getName(), studentVO.getPhone(), currentCourseName));
            errorVO.setOtherCourseTypes(buildCourseTypeInfoList(otherCourseTypes));
            log.info("返回剩余课次为0选择其他类型错误, errorCode: {}, otherCourseTypesCount: {}",
                errorVO.getCode(), errorVO.getOtherCourseTypes().size());
        }

        return R.ok(errorVO);
    }

    /**
     * 构建课程类型信息列表
     */
    private List<ClassStudentErrorVO.CourseTypeInfoVO> buildCourseTypeInfoList(
        List<CourseHoursStudent> courseTypes) {

        log.info("开始构建课程类型信息列表, courseTypesCount: {}", courseTypes.size());

        // 获取所有课程类型信息
        Map<Integer, String> courseTypeMap = new HashMap<>();
        try {
            R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
            if (courseTypeResult.isOk() && CollUtil.isNotEmpty(courseTypeResult.getData())) {
                courseTypeMap = courseTypeResult.getData().stream()
                    .collect(Collectors.toMap(CourseTypeDTO::getId, CourseTypeDTO::getName,
                        (a, b) -> a));
                log.info("获取课程类型名称映射成功, courseTypeMapSize: {}", courseTypeMap.size());
            } else {
                log.warn("获取课程类型信息失败或为空, result: {}", courseTypeResult);
            }
        } catch (Exception e) {
            log.error("获取课程类型信息失败", e);
        }

        final Map<Integer, String> finalCourseTypeMap = courseTypeMap;
        List<ClassStudentErrorVO.CourseTypeInfoVO> result = courseTypes.stream()
            .map(courseType -> {
                ClassStudentErrorVO.CourseTypeInfoVO info =
                    new ClassStudentErrorVO.CourseTypeInfoVO();
                info.setCourseTypeId(courseType.getCourseType());
                String courseTypeName = finalCourseTypeMap.get(courseType.getCourseType());
                info.setCourseTypeName(courseTypeName != null ? courseTypeName : "未知课程类型");
                info.setFormalSessions(courseType.getFormal() != null ? courseType.getFormal() : 0);
                info.setGiftSessions(courseType.getGift() != null ? courseType.getGift() : 0);
                info.setRemainingSessions(
                    courseType.getCourseHours() != null ? courseType.getCourseHours() : 0);

                log.debug(
                    "构建课程类型信息: courseTypeId={}, name={}, remaining={}, formal={}, gift={}",
                    info.getCourseTypeId(), info.getCourseTypeName(), info.getRemainingSessions(),
                    info.getFormalSessions(), info.getGiftSessions());

                return info;
            })
            .collect(Collectors.toList());

        log.info("课程类型信息列表构建完成, resultSize: {}", result.size());
        return result;
    }

    /**
     * 检查答题器解绑提示 判断课次中是否有绑定答题器的学员，没有则返回是否需要弹框提示解绑答题器
     *
     * @param lessonNo 课次号
     * @return ClickerUnbindPromptVO 答题器解绑提示信息
     * <AUTHOR>
     * @date 2025/06/17
     */
    @Override
    public ClickerUnbindPromptVO checkClickerUnbindPrompt(Long lessonNo) {
        log.info("开始检查答题器解绑提示, lessonNo: {}", lessonNo);

        Long storeId = StoreContextHolder.getStoreId();

        // 1. 查询该课次的所有学生记录
        List<BClassTimeStudent> allStudents = this.list(
            Wrappers.<BClassTimeStudent>lambdaQuery()
                .eq(BClassTimeStudent::getLessonNo, lessonNo)
                .eq(BClassTimeStudent::getStoreId, storeId));

        int totalStudentCount = allStudents.size();
        log.info("课次 {} 总学生数: {}", lessonNo, totalStudentCount);

        // 2. 查询已绑定答题器的学生数量（排除分配中状态）
        int boundClickerCount = (int) allStudents.stream()
            .filter(this::isStudentClickerAlreadyBound)
            .count();

        log.info("课次 {} 已绑定答题器学生数: {}", lessonNo, boundClickerCount);

        // 3. 判断是否需要弹框提示
        if (boundClickerCount == 0) {
            // 没有学生绑定答题器，需要弹框提示解绑答题器
            String promptMessage = "课程开始前，请先批量解绑答题器";
            log.info("课次 {} 没有学生绑定答题器，需要弹框提示解绑答题器", lessonNo);
            return ClickerUnbindPromptVO.createPromptVO(lessonNo, boundClickerCount, totalStudentCount,
                promptMessage);
        } else {
            // 有学生绑定答题器，不需要弹框提示
            log.info("课次 {} 有学生绑定答题器，不需要弹框提示", lessonNo);
            return ClickerUnbindPromptVO.createNoPromptVO(lessonNo, boundClickerCount,
                totalStudentCount);
        }
    }


    /**
     * 添加临加学员
     *
     * @param temporaryStudentDTO 临加学员参数
     * @return 操作结果
     * <AUTHOR>
     * @date 2025/06/19
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R addTemporaryStudents(TemporaryStudentDTO temporaryStudentDTO) {
        log.info("开始添加临加学员, 参数: {}", JSON.toJSONString(temporaryStudentDTO));

        Long lessonNo = temporaryStudentDTO.getLessonNo();
        List<Long> studentIds = temporaryStudentDTO.getStudentIds();
        Long storeId = StoreContextHolder.getStoreId();

        // 1. 验证课次是否存在
        getTimetableInfo(lessonNo, storeId);

        // 2. 验证学员是否为在读状态
        R<List<StudentVO>> studentResult = remoteStudentService.getStudentListByIds(studentIds);
        if (!studentResult.isOk() || CollUtil.isEmpty(studentResult.getData())) {
            log.error("获取学员信息失败, studentIds: {}, result: {}", studentIds, studentResult);
            return R.failed("获取学员信息失败");
        }

        List<StudentVO> students = studentResult.getData();
        List<String> invalidStudents = new ArrayList<>();

        for (StudentVO student : students) {
            // 检查学员状态
            if (!isValidStudentStatus(student.getStatus())) {
                invalidStudents.add(student.getName() + "(状态异常)");
            }
            // 检查学员类型，意向会员不允许添加临加学员
            if (Objects.equals(student.getIsRegularStudents(),
                StudentRegularEnum.INTENTION.getCode())) {
                invalidStudents.add(student.getName() + "(意向会员不允许添加临加学员)");
            }
            // 检查试听学员剩余课次 2025年7月24日09:56:36 SP23迭代修改可以添加试听为0的学员
//            if (Objects.equals(student.getStatus(), StudentStatusEnum.TRIAL.getCode())) {
//                Integer remainingLessons = student.getCourseHours();
//                if (remainingLessons == null || remainingLessons <= 0) {
//                    invalidStudents.add(student.getName() + "(试听学员剩余课次为0，无法添加)");
//                }
//            }
        }

        if (!invalidStudents.isEmpty()) {
            String errorMsg = "以下学员状态异常，无法添加: " + String.join(", ", invalidStudents);
            log.warn(errorMsg);
            return R.failed(errorMsg);
        }

        // 3. 检查学员是否已在该课次中（排除已手动移除的学员）
        List<BClassTimeStudent> existingStudents = this.list(
            Wrappers.<BClassTimeStudent>lambdaQuery()
                .eq(BClassTimeStudent::getLessonNo, lessonNo)
                .eq(BClassTimeStudent::getStoreId, storeId)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode())) // 过滤手动移除的学员
                .in(BClassTimeStudent::getStudentId, studentIds));

        if (!existingStudents.isEmpty()) {
            List<String> existingNames = new ArrayList<>();
            Map<Long, StudentVO> studentMap = students.stream()
                .collect(Collectors.toMap(StudentVO::getUserId, Function.identity()));

            for (BClassTimeStudent existing : existingStudents) {
                StudentVO student = studentMap.get(existing.getStudentId());
                if (student != null) {
                    existingNames.add(student.getName());
                }
            }

            String errorMsg = "以下学员已在该课次中: " + String.join(", ", existingNames);
            log.warn(errorMsg);
            return R.failed(errorMsg);
        }

        // 4. 添加临加学员记录
        List<BClassTimeStudent> temporaryStudents = new ArrayList<>();
        for (StudentVO student : students) {
            BClassTimeStudent classTimeStudent = new BClassTimeStudent();
            classTimeStudent.setStoreId(storeId);
            classTimeStudent.setStudentId(student.getUserId());
            classTimeStudent.setStudentType(StudentTypeEnum.STUDENT_TYPE_4.code); // 4-临加学员
            classTimeStudent.setLessonNo(lessonNo);
            classTimeStudent.setSourceLessonNo(null); // 手动添加的临加学员没有来源课次
            classTimeStudent.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 0-未出勤
            classTimeStudent.setAdjustStatus(Integer.valueOf(YesNoEnum.NO.getCode())); // 0-未调出
            classTimeStudent.setIsManuallyRemoved(
                Integer.parseInt(YesNoEnum.NO.getCode())); // 0-未手动移除
            classTimeStudent.setIsRegularStudents(student.getIsRegularStudents());
            temporaryStudents.add(classTimeStudent);
        }

        // 5. 批量保存
        boolean saveResult = this.saveBatch(temporaryStudents);
        if (!saveResult) {
            log.error("保存临加学员记录失败, lessonNo: {}, studentIds: {}", lessonNo, studentIds);
            return R.failed("保存临加学员记录失败");
        }

        log.info("成功添加临加学员, lessonNo: {}, 学员数量: {}", lessonNo,
            temporaryStudents.size());
        return R.ok("成功添加 " + temporaryStudents.size() + " 名临加学员");
    }

    /**
     * 移除临加学员
     *
     * @param temporaryStudentDTO 临加学员参数
     * @return 操作结果
     * <AUTHOR>
     * @date 2025/06/19
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R removeTemporaryStudent(TemporaryStudentDTO temporaryStudentDTO) {
        log.info("开始移除临加学员, 参数: {}", JSON.toJSONString(temporaryStudentDTO));

        Long lessonNo = temporaryStudentDTO.getLessonNo();
        Long studentId = temporaryStudentDTO.getStudentId();
        Long storeId = StoreContextHolder.getStoreId();

        // 1. 查找临加学员记录（只查找未手动移除的记录）
        BClassTimeStudent classTimeStudent = this.getOne(
            Wrappers.<BClassTimeStudent>lambdaQuery()
                .eq(BClassTimeStudent::getLessonNo, lessonNo)
                .eq(BClassTimeStudent::getStudentId, studentId)
                .eq(BClassTimeStudent::getStoreId, storeId)
                .ne(BClassTimeStudent::getIsManuallyRemoved,
                    Integer.parseInt(YesNoEnum.YES.getCode()))); // 只查找未手动移除的记录

        if (classTimeStudent == null) {
            log.warn("未找到学员记录, lessonNo: {}, studentId: {}", lessonNo, studentId);
            return R.failed("未找到学员记录");
        }

        // 2. 验证是否为临加学员
        if (!Objects.equals(classTimeStudent.getStudentType(),
            StudentTypeEnum.STUDENT_TYPE_4.code)) {
            log.warn("该学员不是临加学员，无法移除, studentType: {}",
                classTimeStudent.getStudentType());
            return R.failed("该学员不是临加学员，无法移除");
        }

        // 3. 查询课表信息，判断课程是否已结束
        Timetable timetable = getTimetableInfo(lessonNo, storeId);
        LocalDateTime now = LocalDateTime.now();
        boolean isCourseEnded = now.isAfter(timetable.getClassEndDateTime());

        // 4. 验证移除限制条件
        if (!isCourseEnded) {
            // 课程未结束时，检查答题器绑定状态和签到状态
            boolean hasClickerBound = StringUtils.isNotBlank(classTimeStudent.getClickerSnNumber())
                && !CourseLiveConstant.CLICKER_DISTRIBUTE_ING.equals(classTimeStudent.getClickerSnNumber());
            boolean hasCheckedIn = Objects.equals(classTimeStudent.getCheckInStatus(), CheckInStatusEnum.CHECK_IN_STATUS_1.code);

            if (hasClickerBound) {
                log.warn("课程未结束且该临加学员已绑定答题器，无法移除, lessonNo: {}, studentId: {}, clickerSn: {}",
                    lessonNo, studentId, classTimeStudent.getClickerSnNumber());
                return R.failed("该临加学员已绑定答题器，无法移除");
            }

            if (hasCheckedIn) {
                log.warn("课程未结束且该临加学员已签到考勤，无法移除, lessonNo: {}, studentId: {}", lessonNo, studentId);
                return R.failed("该临加学员已签到考勤，无法移除");
            }
        } else {
            // 课程已结束时，只检查签到状态
            if (Objects.equals(classTimeStudent.getCheckInStatus(),
            CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
                log.warn("该临加学员已签到考勤，无法移除, lessonNo: {}, studentId: {}", lessonNo, studentId);
                return R.failed("该临加学员已签到考勤，无法移除");
            }
        }

        // 5. 验证是否已创建线上补课
        R<String> makeUpCheckResult = checkStudentInOnlineMakeUp(lessonNo, studentId, storeId);
        if (!makeUpCheckResult.isOk()) {
            log.warn("该临加学员已创建线上补课，无法移除, lessonNo: {}, studentId: {}, reason: {}",
                lessonNo, studentId, makeUpCheckResult.getMsg());
            return makeUpCheckResult;
        }

        // 6. 标记为手动移除（不删除记录，避免刷新时重新添加）
        classTimeStudent.setIsManuallyRemoved(Integer.parseInt(YesNoEnum.YES.getCode()));
        boolean updateResult = this.updateById(classTimeStudent);
        if (!updateResult) {
            log.error("标记临加学员为手动移除失败, id: {}", classTimeStudent.getId());
            return R.failed("移除临加学员失败");
        }

        log.info("成功移除临加学员（标记为手动移除）, lessonNo: {}, studentId: {}", lessonNo,
            studentId);
        return R.ok("成功移除临加学员");
    }

    @Override
    public Map<Long, List<Long>> getTemporaryStudentList(List<Long> lessonNoList) {
        log.info("获取临加学员列表, lessonNoList: {}", lessonNoList);
        if (CollUtil.isEmpty(lessonNoList)) {
            return Map.of();
        }
        return bClassTimeStudentMapper.selectList(Wrappers.<BClassTimeStudent>lambdaQuery()
            .in(BClassTimeStudent::getLessonNo, lessonNoList)
            .eq(BClassTimeStudent::getAdjustStatus, Integer.valueOf(YesNoEnum.NO.getCode()))
            .eq(BClassTimeStudent::getIsManuallyRemoved, Integer.valueOf(YesNoEnum.NO.getCode()))
            .in(BClassTimeStudent::getStudentType,
                List.of(StudentTypeEnum.STUDENT_TYPE_2.code, StudentTypeEnum.STUDENT_TYPE_4.code)))
            .stream()
            .collect(Collectors.groupingBy(BClassTimeStudent::getLessonNo,
                Collectors.mapping(BClassTimeStudent::getStudentId, Collectors.toList())));
    }

    /**
     * 验证学员状态是否有效(在读状态)
     */
    private boolean isValidStudentStatus(Integer status) {
        return Objects.equals(status, StudentStatusEnum.FORMAL.getCode()) ||
            Objects.equals(status, StudentStatusEnum.TRIAL.getCode()) ||
            Objects.equals(status, StudentStatusEnum.SUSPENSION.getCode());
    }

    /**
     * 检查学员是否已在该课次的线上补课中
     *
     * @param lessonNo  课次编号
     * @param studentId 学员ID
     * @param storeId   门店ID
     * @return 检查结果
     */
    private R<String> checkStudentInOnlineMakeUp(Long lessonNo, Long studentId, Long storeId) {
        try {
            // 1. 查询该课次是否存在线上补课记录
            List<BCourseMakeUpOnline> makeUpOnlineList = bCourseMakeUpOnlineMapper.selectList(
                Wrappers.lambdaQuery(BCourseMakeUpOnline.class)
                    .eq(BCourseMakeUpOnline::getLessonNo, lessonNo)
                    .eq(BCourseMakeUpOnline::getStoreId, storeId)
            );

            if (CollectionUtils.isEmpty(makeUpOnlineList)) {
                // 没有线上补课记录，允许移除
                return R.ok();
            }

            log.info("找到{}条线上补课记录, lessonNo: {}", makeUpOnlineList.size(), lessonNo);

            // 2. 检查该学员是否参与了任何一个线上补课
            for (BCourseMakeUpOnline makeUpOnline : makeUpOnlineList) {
                // 获取原课表信息以生成补课课次号
                Timetable timetable = timetableMapper.selectOne(
                    Wrappers.lambdaQuery(Timetable.class)
                        .eq(Timetable::getLessonNo, lessonNo)
                        .eq(Timetable::getStoreId, storeId)
                );

                if (timetable == null) {
                    log.warn("未找到原课表信息, lessonNo: {}", lessonNo);
                    continue;
                }

                // 生成补课课次号
                String makeUpLessonNo = CourseTypeEnum.COURSE_TYPE_ENUM_4.code
                    + timetable.getLessonOrder().toString()
                    + makeUpOnline.getId().toString();

                // 检查该学员是否在此补课中存在记录
                long count = this.count(
                    Wrappers.lambdaQuery(BClassTimeStudent.class)
                        .eq(BClassTimeStudent::getLessonNo, Long.valueOf(makeUpLessonNo))
                        .eq(BClassTimeStudent::getStudentId, studentId)
                        .eq(BClassTimeStudent::getStoreId, storeId)
                );

                if (count > 0) {
                    log.warn("学员已参与线上补课，无法移除, studentId: {}, makeUpLessonNo: {}",
                        studentId, makeUpLessonNo);
                    return R.failed("该临加学员已创建线上补课，无法移除");
                }
            }

            // 没有参与任何线上补课，允许移除
            return R.ok();

        } catch (Exception e) {
            log.error("检查学员线上补课状态时发生异常, lessonNo: {}, studentId: {}", lessonNo,
                studentId, e);
            return R.failed("检查线上补课状态失败，请稍后重试");
        }
    }

    /**
     * 为补课创建临加学员记录，设置来源课次
     *
     * @param temporaryStudents 原课次临加学员列表
     * @param makeupLessonNo    补课课次编号
     * @param originalLessonNo  原课次编号
     * @param storeId           门店ID
     * @param existingStudents  已存在的学生记录
     * @return 需要添加的补课临加学员记录
     * @date 2025/07/02
     */
    private List<BClassTimeStudent> createMakeupTemporaryStudents(
        List<StudentVO> temporaryStudents, Long makeupLessonNo, Long originalLessonNo,
        Long storeId, List<BClassTimeStudent> existingStudents) {

        if (CollUtil.isEmpty(temporaryStudents) || originalLessonNo == null) {
            return Collections.emptyList();
        }

        // 获取已存在学生的ID集合
        Set<Long> existingStudentIds = existingStudents.stream()
            .map(BClassTimeStudent::getStudentId)
            .collect(Collectors.toSet());

        List<BClassTimeStudent> makeupTemporaryRecords = new ArrayList<>();

        for (StudentVO student : temporaryStudents) {
            // 跳过已存在的学生
            if (existingStudentIds.contains(student.getUserId())) {
                log.debug("学生 {} 已存在于补课课次中，跳过创建", student.getUserId());
                continue;
            }

            BClassTimeStudent record = new BClassTimeStudent();
            record.setStoreId(storeId);
            record.setStudentId(student.getUserId());
            record.setStudentType(StudentTypeEnum.STUDENT_TYPE_4.code); // 4-临加学员
            record.setLessonNo(makeupLessonNo);
            record.setSourceLessonNo(originalLessonNo); // 设置来源课次
            record.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 0-未出勤
            record.setAdjustStatus(Integer.valueOf(YesNoEnum.NO.getCode())); // 0-未调出
            record.setIsManuallyRemoved(Integer.parseInt(YesNoEnum.NO.getCode())); // 0-未手动移除
            record.setIsRegularStudents(student.getIsRegularStudents());

            makeupTemporaryRecords.add(record);
            log.debug("创建补课临加学员记录: studentId={}, makeupLessonNo={}, originalLessonNo={}",
                student.getUserId(), makeupLessonNo, originalLessonNo);
        }

        log.info("为补课课次 {} 创建了 {} 条临加学员记录，来源课次: {}",
            makeupLessonNo, makeupTemporaryRecords.size(), originalLessonNo);

        return makeupTemporaryRecords;
    }

    /**
     * 获取补课对应的原课次编号
     *
     * @param makeupTimetable 补课课表信息
     * @param storeId         门店ID
     * @return 原课次编号，如果未找到返回null
     * @date 2025/07/02
     */
    private Long getOriginalLessonNo(Timetable makeupTimetable, Long storeId) {
        try {
            // 通过补课课表ID查询补课记录，获取原课表ID
            CourseMakeUp courseMakeUp = courseMakeUpMapper.selectOne(
                Wrappers.lambdaQuery(CourseMakeUp.class)
                    .eq(CourseMakeUp::getId, makeupTimetable.getCoursePlanId())
                    .eq(CourseMakeUp::getStoreId, storeId)
            );

            if (courseMakeUp == null || courseMakeUp.getTimetableId() == null) {
                log.warn("未找到补课记录或原课表ID为空, 补课ID: {}",
                    makeupTimetable.getCoursePlanId());
                return null;
            }

            // 通过原课表ID查询原课次信息
            Timetable originalTimetable = timetableMapper.selectOne(
                Wrappers.lambdaQuery(Timetable.class)
                    .eq(Timetable::getId, courseMakeUp.getTimetableId())
                    .eq(Timetable::getStoreId, storeId)
            );

            if (originalTimetable == null) {
                log.warn("未找到原课表信息, 原课表ID: {}", courseMakeUp.getTimetableId());
                return null;
            }

            return originalTimetable.getLessonNo();
        } catch (Exception e) {
            log.error("获取原课次编号时发生异常, 补课课表ID: {}, 门店ID: {}",
                makeupTimetable.getId(), storeId, e);
            return null;
        }
    }

    /**
     * 获取原课次的临时学员列表（用于补课场景）
     *
     * @param timetable 补课课表信息
     * @param storeId   门店ID
     * @return 原课次的临时学员列表
     * @date 2025/07/01
     */
    private List<StudentVO> getTemporaryStudentsFromOriginalLesson(Timetable timetable,
        Long storeId) {
        try {
            log.info("开始查询补课原课次的临时学员, 补课课表ID: {}, 门店ID: {}", timetable.getId(),
                storeId);

            // 1. 获取原课次编号
            Long originalLessonNo = getOriginalLessonNo(timetable, storeId);
            if (originalLessonNo == null) {
                return Collections.emptyList();
            }

            log.info("找到原课次号: {}", originalLessonNo);

            // 2. 查询原课次中的临时学员（student_type=4）
            List<BClassTimeStudent> temporaryStudents = this.list(
                Wrappers.lambdaQuery(BClassTimeStudent.class)
                    .eq(BClassTimeStudent::getLessonNo, originalLessonNo)
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .eq(BClassTimeStudent::getStudentType, StudentTypeEnum.STUDENT_TYPE_4.code)
                    .eq(BClassTimeStudent::getDelFlag, YesNoEnum.NO.getCode())
            );

            if (CollUtil.isEmpty(temporaryStudents)) {
                log.info("原课次中没有临时学员, 原课次号: {}", originalLessonNo);
                return Collections.emptyList();
            }

            log.info("找到原课次临时学员数量: {}", temporaryStudents.size());

            // 3. 检查这些临时学员是否已在补课中被手动移除
            List<Long> studentIds = temporaryStudents.stream()
                .map(BClassTimeStudent::getStudentId)
                .collect(Collectors.toList());

            // 查询补课课次中已手动移除的学员
            List<BClassTimeStudent> manuallyRemovedStudents = this.list(
                Wrappers.lambdaQuery(BClassTimeStudent.class)
                    .eq(BClassTimeStudent::getLessonNo, timetable.getLessonNo())
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .eq(BClassTimeStudent::getSourceLessonNo, originalLessonNo)
                    .eq(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode()))
                    .in(BClassTimeStudent::getStudentId, studentIds)
            );

            Set<Long> removedStudentIds = manuallyRemovedStudents.stream()
                .map(BClassTimeStudent::getStudentId)
                .collect(Collectors.toSet());

            // 过滤掉已手动移除的学员
            List<Long> validStudentIds = studentIds.stream()
                .filter(studentId -> !removedStudentIds.contains(studentId))
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(validStudentIds)) {
                log.info("所有原课次临时学员都已被手动移除, 原课次号: {}, 补课课次号: {}",
                    originalLessonNo, timetable.getLessonNo());
                return Collections.emptyList();
            }

            log.info("过滤手动移除后的有效临时学员数量: {}, 原数量: {}", validStudentIds.size(),
                studentIds.size());

            // 4. 获取有效临时学员的详细信息
            R<List<StudentVO>> studentResult = remoteStudentService.getStudentListByIds(
                validStudentIds);
            if (!studentResult.isOk() || CollUtil.isEmpty(studentResult.getData())) {
                log.warn("获取临时学员详细信息失败, studentIds: {}, result: {}", validStudentIds,
                    studentResult);
                return Collections.emptyList();
            }

            List<StudentVO> studentVOList = studentResult.getData();

            // 5. 为每个学员设置来源课次信息（用于后续保存时设置source_lesson_no）
            studentVOList.forEach(student -> {
                // 这里可以通过扩展StudentVO或使用其他方式传递原课次信息
                // 暂时通过日志记录，实际保存时会在调用方设置
                log.debug("临时学员 {} 来源于原课次: {}", student.getUserId(), originalLessonNo);
            });

            log.info("成功获取有效的原课次临时学员详细信息, 数量: {}", studentVOList.size());

            return studentVOList;

        } catch (Exception e) {
            log.error("获取原课次临时学员时发生异常, 补课课表ID: {}, 门店ID: {}", timetable.getId(),
                storeId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 确保已结束的补课课程包含原课次的临时学员
     *
     * @param timetable 补课课表信息
     * @param storeId   门店ID
     * @param lessonNo  课次号
     * <AUTHOR>
     * @date 2025/07/01
     */
    private void ensureTemporaryStudentsForEndedMakeupCourse(Timetable timetable, Long storeId,
        Long lessonNo) {
        try {
            log.info("开始为已结束的补课课程补充原课次临时学员, lessonNo: {}", lessonNo);

            // 1. 获取原课次的临时学员
            List<StudentVO> temporaryStudents = getTemporaryStudentsFromOriginalLesson(timetable,
                storeId);
            if (CollUtil.isEmpty(temporaryStudents)) {
                log.info("原课次中没有临时学员需要补充, lessonNo: {}", lessonNo);
                return;
            }

            log.info("找到原课次临时学员数量: {}", temporaryStudents.size());

            // 2. 检查这些临时学员是否已经存在于当前补课的学生记录中
            List<Long> temporaryStudentIds = temporaryStudents.stream()
                .map(StudentVO::getUserId)
                .collect(Collectors.toList());

            List<BClassTimeStudent> existingRecords = this.list(
                Wrappers.lambdaQuery(BClassTimeStudent.class)
                    .eq(BClassTimeStudent::getLessonNo, lessonNo)
                    .eq(BClassTimeStudent::getStoreId, storeId)
                    .ne(BClassTimeStudent::getIsManuallyRemoved,
                        Integer.parseInt(YesNoEnum.YES.getCode())) // 过滤手动移除的学员
                    .in(BClassTimeStudent::getStudentId, temporaryStudentIds)
            );

            Set<Long> existingStudentIds = existingRecords.stream()
                .map(BClassTimeStudent::getStudentId)
                .collect(Collectors.toSet());

            // 3. 筛选出需要添加的临时学员
            List<StudentVO> studentsToAdd = temporaryStudents.stream()
                .filter(student -> !existingStudentIds.contains(student.getUserId()))
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(studentsToAdd)) {
                log.info("所有原课次临时学员都已存在于补课记录中, lessonNo: {}", lessonNo);
                return;
            }

            log.info("需要补充的临时学员数量: {}", studentsToAdd.size());

            // 4. 获取原课次编号
            Long originalLessonNo = getOriginalLessonNo(timetable, storeId);

            // 5. 创建临时学员记录
            List<BClassTimeStudent> temporaryRecords = new ArrayList<>();
            for (StudentVO student : studentsToAdd) {
                BClassTimeStudent record = new BClassTimeStudent();
                record.setStoreId(storeId);
                record.setStudentId(student.getUserId());
                record.setStudentType(StudentTypeEnum.STUDENT_TYPE_4.code); // 4-临加学员
                record.setLessonNo(lessonNo);
                record.setSourceLessonNo(originalLessonNo); // 设置来源课次
                record.setCheckInStatus(CheckInStatusEnum.CHECK_IN_STATUS_0.code); // 0-未出勤
                record.setAdjustStatus(Integer.valueOf(YesNoEnum.NO.getCode())); // 0-未调出
                record.setIsManuallyRemoved(Integer.parseInt(YesNoEnum.NO.getCode())); // 0-未手动移除
                record.setIsRegularStudents(student.getIsRegularStudents());
                temporaryRecords.add(record);
            }

            // 6. 批量插入临时学员记录
            this.saveBatch(temporaryRecords);
            log.info(
                "成功为已结束的补课课程补充原课次临时学员, 补充数量: {}, lessonNo: {}, 原课次: {}",
                temporaryRecords.size(), lessonNo, originalLessonNo);

        } catch (Exception e) {
            log.error("为已结束的补课课程补充原课次临时学员时发生异常, lessonNo: {}", lessonNo, e);
        }
    }

    /**
     * 获取考勤确认列表
     *
     * @param lessonno 课次编号
     * @param storeId 门店ID
     * @return 考勤确认列表，包含预考勤状态
     * <AUTHOR>
     * @date 2025/07/08
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @ForceMaster
    public CheckInStudentVO getAttendanceConfirmationList(Serializable lessonno, Long storeId) {
        log.info("开始获取考勤确认列表, lessonNo: {}", lessonno);

        // 1. 复用现有的 getCheckInStudent 方法获取出勤学生列表
        CheckInStudentVO checkInStudentVO = getCheckInStudent(lessonno, CheckInEntryTypeEnum.CHECK_IN_ENTRY_TYPE_2.code, storeId);

        if (checkInStudentVO == null || CollectionUtils.isEmpty(checkInStudentVO.getStudents())) {
            log.info("课次 {} 没有出勤学生", lessonno);
            return checkInStudentVO;
        }

        // 2. 查询课表信息获取课次ID
        Long lessonNoLong = Long.valueOf(lessonno.toString());
        Timetable timetable = getTimetableInfo(lessonNoLong, storeId);

        // 3. 检查当前课次是否已有学生确认过考勤
        boolean hasCheckedInStudents = checkIfLessonHasCheckedInStudents(lessonNoLong, storeId);

        if (hasCheckedInStudents) {
            log.info("课次 {} 已有学生确认过考勤，跳过预考勤状态判断", lessonno);
        } else {
            log.info("课次 {} 暂无学生确认考勤，执行预考勤状态判断", lessonno);
            // 4. 为每个学生判断预考勤状态和原始考勤状态
            List<StudentCheckInInfoVO> students = checkInStudentVO.getStudents();
            for (StudentCheckInInfoVO student : students) {
                // 判断预考勤状态和原始考勤状态
                setAttendanceStatus(student, timetable);
            }
        }

        log.info("考勤确认列表获取完成, lessonNo: {}, 学生数: {}", lessonno, checkInStudentVO.getStudents().size());
        return checkInStudentVO;
    }

    /**
     * 检查当前课次是否已有学生确认过考勤
     *
     * @param lessonNo 课次编号
     * @param storeId 门店ID
     * @return true-已有学生确认过考勤；false-暂无学生确认考勤
     */
    private boolean checkIfLessonHasCheckedInStudents(Long lessonNo, Long storeId) {
        log.debug("检查课次 {} 是否已有学生确认过考勤", lessonNo);
        // 查询当前课次中是否存在 check_in_status = 1（已考勤）的学生记录
        long checkedInCount = baseMapper.selectCount(
            Wrappers.<BClassTimeStudent>lambdaQuery()
                .eq(BClassTimeStudent::getLessonNo, lessonNo)
                .eq(BClassTimeStudent::getStoreId, storeId)
                .eq(BClassTimeStudent::getCheckInStatus, CheckInStatusEnum.CHECK_IN_STATUS_1.code)
        );
        boolean hasCheckedIn = checkedInCount > 0;
        log.debug("课次 {} 已确认考勤学生数: {}, 是否已有确认考勤: {}", lessonNo, checkedInCount, hasCheckedIn);

        return hasCheckedIn;
    }

    /**
     * 设置学生的考勤状态（预考勤状态和原始考勤状态）
     *
     * @param student 学生信息
     * @param timetable 课表信息
     */
    private void setAttendanceStatus(StudentCheckInInfoVO student, Timetable timetable) {
        try {
            // 1. 判断原始考勤状态
            Integer originalStatus;
            boolean preAttendanceStatus = false;

            // 如果已经签到，原始状态为"已考勤"
            if (Objects.equals(student.getCheckInStatus(), CheckInStatusEnum.CHECK_IN_STATUS_1.code)) {
                originalStatus = OriginalStatusType.CHECKED_IN.getCode(); // 已考勤
            } else {
                // 未签到的学员，检查是否满足预考勤条件
                boolean hasInteractionData = checkInteractionData(student, timetable);

                if (hasInteractionData) {
                    // 有互动数据，进行课消校验
                    boolean courseHourValidationPassed = validateCourseHours(student, timetable);

                    if (courseHourValidationPassed) {
                        // 课消校验通过，为预考勤状态
                        originalStatus = OriginalStatusType.PRE_ATTENDANCE.getCode(); // 预考勤
                        preAttendanceStatus = true;
                        log.debug("学生 {} 满足预考勤条件（有互动数据且课消校验通过）", student.getStudentId());
                    } else {
                        // 课消校验不通过，为未考勤状态
                        originalStatus = OriginalStatusType.NOT_CHECKED_IN.getCode(); // 未考勤
                        log.debug("学生 {} 有互动数据但课消校验不通过", student.getStudentId());
                    }
                } else {
                    // 无互动数据，为未考勤状态
                    originalStatus = OriginalStatusType.NOT_CHECKED_IN.getCode(); // 未考勤
                    log.debug("学生 {} 无互动数据", student.getStudentId());
                }
            }

            // 设置状态
            student.setOriginalAttendanceStatus(originalStatus);
            student.setPreAttendanceStatus(preAttendanceStatus);

        } catch (Exception e) {
            log.error("设置学生 {} 考勤状态异常", student.getStudentId(), e);
            // 异常情况下设置为未考勤状态
            student.setOriginalAttendanceStatus(0);
            student.setPreAttendanceStatus(false);
        }
    }

    /**
     * 验证学生课消是否正常
     * 通过调用 preValidateCheckIn 方法进行校验
     *
     * @param student 学生信息
     * @param timetable 课表信息
     * @return true-课消校验通过；false-课消校验不通过
     */
    private boolean validateCourseHours(StudentCheckInInfoVO student, Timetable timetable) {
        try {
            // 构建预校验请求参数
            StudentCheckInPreValidationDTO preValidationDTO = new StudentCheckInPreValidationDTO();
            preValidationDTO.setStudentId(student.getStudentId());
            preValidationDTO.setLessonNo(timetable.getLessonNo());
            preValidationDTO.setStoreId(timetable.getStoreId());
            preValidationDTO.setForceCheckIn(Integer.parseInt(YesNoEnum.NO.getCode()));

            // 调用预校验方法
            R<ClassStudentErrorVO> validationResult = preValidateCheckIn(preValidationDTO);

            // 判断校验结果
            if (validationResult != null && validationResult.getCode() == 0) {
                ClassStudentErrorVO errorVO = validationResult.getData();
                if (errorVO != null && errorVO.getCode() == 0) {
                    // 校验通过
                    log.debug("学生 {} 课消校验通过", student.getStudentId());
                    return true;
                } else {
                    // 有错误码，校验不通过
                    log.debug("学生 {} 课消校验不通过，错误码: {}", student.getStudentId(),
                        errorVO != null ? errorVO.getCode() : "unknown");
                    return false;
                }
            } else {
                // 接口调用失败
                log.debug("学生 {} 课消校验接口调用失败", student.getStudentId());
                return false;
            }

        } catch (Exception e) {
            log.error("学生 {} 课消校验异常", student.getStudentId(), e);
            return false;
        }
    }



    /**
     * 检查学生是否产生互动数据
     * 通过远程调用查询 ss_interaction_consequence 表判断
     *
     * @param student 学生信息
     * @param timetable 课表信息
     * @return true-有互动数据；false-无互动数据
     */
    private boolean checkInteractionData(StudentCheckInInfoVO student, Timetable timetable) {
        try {
            // 通过本地 Mapper 查询学生是否有互动数据
            // 使用课次编号和教室ID来查询
            long count = ssInteractionConsequenceMapper.countStudentInteraction(
                timetable.getId(),
                student.getStudentId()
            );

            boolean hasInteraction = count > 0;
            log.debug("学生 {} 在课次编号 {} 教室 {} 的互动数据检查结果: {} (记录数: {})",
                student.getStudentId(), timetable.getLessonNo(), timetable.getClassroomId(), hasInteraction, count);
            return hasInteraction;

        } catch (Exception e) {
            log.error("检查学生 {} 互动数据异常, lessonNo: {}, classroomId: {}",
                student.getStudentId(), timetable.getLessonNo(), timetable.getClassroomId(), e);
            // 异常情况下返回 false，表示无互动数据
            return false;
        }
    }

    /**
     * 批量确认考勤
     *
     * @param confirmationDTO 考勤确认请求参数
     * @return 考勤确认结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<AttendanceConfirmationResultVO> confirmAttendanceBatch(AttendanceConfirmationDTO confirmationDTO) {
        log.info("开始执行批量确认考勤, 参数: {}", JSON.toJSONString(confirmationDTO));

        Long lessonNo = confirmationDTO.getLessonNo();
        Long storeId = StoreContextHolder.getStoreId();
        List<AttendanceConfirmationDTO.StudentAttendanceOperationDTO> studentOperations = confirmationDTO.getStudentOperations();

        // 1. 基础校验
        Timetable timetable = getTimetableInfo(lessonNo, storeId);

        // 2. 批量预校验需要扣减课时的学员
        List<AttendanceConfirmationResultVO.StudentErrorResultVO> errorResults = new ArrayList<>();
        List<AttendanceConfirmationDTO.StudentAttendanceOperationDTO> validOperations = new ArrayList<>();

        for (AttendanceConfirmationDTO.StudentAttendanceOperationDTO operation : studentOperations) {
            if (needsCourseHourDeduction(operation)) {
                R<ClassStudentErrorVO> validationResult = performPreValidation(operation, lessonNo, storeId);
                if (validationResult.getData() != null && validationResult.getData().getCode() != 0) {
                    // 有错误需要用户处理
                    StudentVO studentVO = getStudentInfo(operation.getStudentId());
                    AttendanceConfirmationResultVO.StudentErrorResultVO errorResult = new AttendanceConfirmationResultVO.StudentErrorResultVO();
                    errorResult.setStudentId(operation.getStudentId());
                    errorResult.setStudentName(studentVO.getName());
                    errorResult.setStudentPhone(studentVO.getPhone());
                    errorResult.setOperationType(operation.getTargetOperation());
                    errorResult.setErrorInfo(validationResult.getData());
                    errorResults.add(errorResult);
                } else {
                    validOperations.add(operation);
                }
            } else {
                validOperations.add(operation);
            }
        }

        // 3. 如果有错误，返回错误信息供用户处理
        // 无论是首次提交还是二次确认，只要有错误都应该让用户处理
        if (!errorResults.isEmpty()) {
            log.info("发现需要用户处理的错误, 错误数量: {}, 是否二次确认: {}",
                errorResults.size(), confirmationDTO.getIsSecondConfirmation());
            return R.ok(AttendanceConfirmationResultVO.createPartialFailureResult(
                List.of(), List.of(), errorResults));
        }

        // 4. 执行批量操作
        List<AttendanceConfirmationResultVO.StudentOperationResultVO> successResults = new ArrayList<>();
        List<AttendanceConfirmationResultVO.StudentOperationResultVO> failureResults = new ArrayList<>();

        for (AttendanceConfirmationDTO.StudentAttendanceOperationDTO operation : validOperations) {
            try {
                AttendanceConfirmationResultVO.StudentOperationResultVO result = executeStudentOperation(operation, lessonNo, storeId);
                if (result.getSuccess()) {
                    successResults.add(result);
                } else {
                    failureResults.add(result);
                }
            } catch (Exception e) {
                log.error("执行学员操作异常, studentId: {}, operation: {}", operation.getStudentId(), operation.getTargetOperation(), e);
                StudentVO studentVO = getStudentInfo(operation.getStudentId());
                AttendanceConfirmationResultVO.StudentOperationResultVO failureResult = new AttendanceConfirmationResultVO.StudentOperationResultVO();
                failureResult.setStudentId(operation.getStudentId());
                failureResult.setStudentName(studentVO.getName());
                failureResult.setStudentPhone(studentVO.getPhone());
                failureResult.setOperationType(operation.getTargetOperation());
                failureResult.setResultMessage("操作异常: " + e.getMessage());
                failureResult.setSuccess(false);
                failureResults.add(failureResult);
            }
        }

        // 5. 返回结果
        if (failureResults.isEmpty() && errorResults.isEmpty()) {
            return R.ok(AttendanceConfirmationResultVO.createAllSuccessResult(successResults));
        } else {
            return R.ok(AttendanceConfirmationResultVO.createPartialFailureResult(
                successResults, failureResults, errorResults));
        }
    }

    /**
     * 判断操作是否需要扣减课时
     */
    private boolean needsCourseHourDeduction(AttendanceConfirmationDTO.StudentAttendanceOperationDTO operation) {
        Integer targetOperation = operation.getTargetOperation();
        // 签到(1)和补签(3)需要扣减课时
        return AttendanceConfirmationDTO.TargetOperationType.CHECK_IN.getCode().equals(targetOperation) ||
               AttendanceConfirmationDTO.TargetOperationType.REISSUE_CHECK_IN.getCode().equals(targetOperation);
    }

    /**
     * 执行预校验
     */
    private R<ClassStudentErrorVO> performPreValidation(AttendanceConfirmationDTO.StudentAttendanceOperationDTO operation, Long lessonNo, Long storeId) {
        StudentCheckInPreValidationDTO preValidationDTO = new StudentCheckInPreValidationDTO();
        preValidationDTO.setStudentId(operation.getStudentId());
        preValidationDTO.setLessonNo(lessonNo);
        preValidationDTO.setStoreId(storeId);
        preValidationDTO.setForceCheckIn(operation.getForceCheckIn());
        preValidationDTO.setSelectedCourseType(operation.getCourseTypeId());

        return preValidateCheckIn(preValidationDTO);
    }

    /**
     * 执行单个学员操作
     */
    private AttendanceConfirmationResultVO.StudentOperationResultVO executeStudentOperation(
            AttendanceConfirmationDTO.StudentAttendanceOperationDTO operation, Long lessonNo, Long storeId) {

        StudentVO studentVO = getStudentInfo(operation.getStudentId());
        AttendanceConfirmationResultVO.StudentOperationResultVO result = new AttendanceConfirmationResultVO.StudentOperationResultVO();
        result.setStudentId(operation.getStudentId());
        result.setStudentName(studentVO.getName());
        result.setStudentPhone(studentVO.getPhone());
        result.setOperationType(operation.getTargetOperation());

        try {
            Integer targetOperation = operation.getTargetOperation();

            if (AttendanceConfirmationDTO.TargetOperationType.NO_OPERATION.getCode().equals(targetOperation)) {
                // 无操作
                result.setResultMessage("无需操作");
                result.setSuccess(true);
                return result;
            }

            // 构建操作DTO
            BClassTimeStudentDTO operationDTO = new BClassTimeStudentDTO();
            operationDTO.setStudentId(operation.getStudentId());
            operationDTO.setLessonNo(lessonNo);
            operationDTO.setStoreId(storeId);
            operationDTO.setForceCheckIn(operation.getForceCheckIn());
            operationDTO.setCourseTypeId(operation.getCourseTypeId());

            R<ClassStudentErrorVO> operationResult;
            String operationName;

            if (AttendanceConfirmationDTO.TargetOperationType.CHECK_IN.getCode().equals(targetOperation)) {
                // 签到
                operationResult = checkIn(operationDTO);
                operationName = AttendanceConfirmationDTO.TargetOperationType.CHECK_IN.getDesc();
            } else if (AttendanceConfirmationDTO.TargetOperationType.CANCEL_CHECK_IN.getCode().equals(targetOperation)) {
                // 取消考勤
                operationResult = cancelCheckIn(operationDTO);
                operationName = AttendanceConfirmationDTO.TargetOperationType.CANCEL_CHECK_IN.getDesc();
            } else if (AttendanceConfirmationDTO.TargetOperationType.REISSUE_CHECK_IN.getCode().equals(targetOperation)) {
                // 补签
                operationResult = chckinReissue(operationDTO, false);
                operationName = AttendanceConfirmationDTO.TargetOperationType.REISSUE_CHECK_IN.getDesc();
            } else {
                result.setResultMessage("不支持的操作类型: " + targetOperation);
                result.setSuccess(false);
                return result;
            }

            // 处理操作结果
            if (operationResult.isOk()) {
                ClassStudentErrorVO errorVO = operationResult.getData();
                if (errorVO != null && errorVO.getCode() != Integer.parseInt(YesNoEnum.NO.getCode())) {
                    result.setResultMessage(operationName + "失败: " + errorVO.getMsg());
                    result.setSuccess(false);
                } else {
                    result.setResultMessage(operationName + "成功");
                    result.setSuccess(true);
                }
            } else {
                result.setResultMessage(operationName + "失败: " + operationResult.getMsg());
                result.setSuccess(false);
            }

        } catch (Exception e) {
            log.error("执行学员操作异常, studentId: {}, operation: {}", operation.getStudentId(), operation.getTargetOperation(), e);
            result.setResultMessage("操作异常: " + e.getMessage());
            result.setSuccess(false);
        }

        return result;
    }

    /**
     * 向已出勤学生发送通知
     *
     * @param lessonNo 课次编号
     * @param storeId 门店ID
     * @return 发送结果
     * @date 2025/07/14
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R sendNotificationToAttendedStudents(Long lessonNo, Long storeId) {
        log.info("开始向已出勤学生发送通知, lessonNo: {}, storeId: {}", lessonNo, storeId);

        try {
            // 1. 检查门店课消通知开关
            if (!isAttendanceNotificationEnabled(storeId)) {
                log.info("门店[{}]未开启课消通知功能，跳过发送", storeId);
                return R.ok("操作成功");
            }

            // 2. 查询已出勤的学生列表
            List<StudentCheckInInfoVO> attendedStudents = getCheckedInStudentsByLessonNo(lessonNo, storeId);
            if (CollectionUtils.isEmpty(attendedStudents)) {
                log.info("课次[{}]门店[{}]暂无已出勤学生", lessonNo, storeId);
                return R.ok("操作成功");
            }

            log.info("找到{}名已出勤学生", attendedStudents.size());

            // 2. 获取学生的微信openId信息
            List<Long> studentIds = attendedStudents.stream()
                .map(StudentCheckInInfoVO::getStudentId)
                .collect(Collectors.toList());

            R<List<StudentMemberDTO>> studentMembersResult = remoteStudentService.getByStudentIdList(studentIds);
            if (!studentMembersResult.isOk() || CollectionUtils.isEmpty(studentMembersResult.getData())) {
                log.warn("未找到学生的微信openId信息, lessonNo: {}, studentIds: {}", lessonNo, studentIds);
                return R.ok("操作成功");
            }
            List<StudentMemberDTO> studentMembers = studentMembersResult.getData();

            // 3. 创建学生ID到openId的映射
            Map<Long, String> studentOpenIdMap = studentMembers.stream()
                .filter(member -> StringUtils.isNotBlank(member.getPublicOpenId()))
                .collect(Collectors.toMap(StudentMemberDTO::getUserId, StudentMemberDTO::getPublicOpenId));

            if (studentOpenIdMap.isEmpty()) {
                log.warn("没有学生绑定微信公众号, lessonNo: {}, storeId: {}", lessonNo, storeId);
                return R.ok("操作成功");
            }

            // 4. 检查是否已经发送过通知（防止重复发送）
            List<WxStudentMsg> existingMessages = wxStudentMsgService.list(
                Wrappers.<WxStudentMsg>lambdaQuery()
                    .eq(WxStudentMsg::getObjId, lessonNo)
                    .eq(WxStudentMsg::getStoreId, storeId)
                    .eq(WxStudentMsg::getSendStatus, WX_MSG_SEND_STATUS_SENDED)
                    .eq(WxStudentMsg::getRepEvent, WX_MSG_REP_EVENT_COURSE_REMINDER)
                    .in(WxStudentMsg::getStudentId, studentIds)
            );

            Set<Long> alreadySentStudentIds = existingMessages.stream()
                .map(WxStudentMsg::getStudentId)
                .collect(Collectors.toSet());

            // 5. 过滤出需要发送通知的学生
            List<StudentCheckInInfoVO> studentsToNotify = attendedStudents.stream()
                .filter(student -> studentOpenIdMap.containsKey(student.getStudentId()))
                .filter(student -> !alreadySentStudentIds.contains(student.getStudentId()))
                .collect(Collectors.toList());

            if (studentsToNotify.isEmpty()) {
                log.info("所有学生都已发送过通知或未绑定微信, lessonNo: {}, storeId: {}", lessonNo, storeId);
                return R.ok("操作成功");
            }

            log.info("需要发送通知的学生数量: {}, lessonNo: {}, storeId: {}", studentsToNotify.size(), lessonNo, storeId);

            // 7. 先保存数据库记录，再异步发送通知
            return saveRecordsAndSendNotifications(lessonNo, storeId, studentsToNotify, studentOpenIdMap);

        } catch (Exception e) {
            log.error("向已出勤学生发送通知失败, lessonNo: {}, storeId: {}, error: {}", lessonNo, storeId, e.getMessage(), e);
            return R.ok("操作成功");
        }
    }

    /**
     * 检查门店是否开启课消通知功能
     *
     * 重构后委托给通用通知服务
     *
     * @param storeId 门店ID
     * @return 是否开启
     */
    private boolean isAttendanceNotificationEnabled(Long storeId) {
        return studentAttendanceNotificationService.isAttendanceNotificationEnabled(storeId);
    }

    /**
     * 保存数据库记录并异步发送通知
     *
     * 重构后的方法，使用通用的通知服务
     *
     * @param lessonNo 课次编号
     * @param storeId 门店ID
     * @param studentsToNotify 需要通知的学生列表
     * @param studentOpenIdMap 学生ID到openId的映射
     * @return 操作结果
     */
    private R saveRecordsAndSendNotifications(Long lessonNo, Long storeId,
                                            List<StudentCheckInInfoVO> studentsToNotify,
                                            Map<Long, String> studentOpenIdMap) {
        try {
            // 1. 获取课次信息
            Timetable timetable = timetableMapper.selectOne(
                Wrappers.<Timetable>lambdaQuery()
                    .eq(Timetable::getLessonNo, lessonNo)
                    .eq(Timetable::getStoreId, storeId)
            );

            if (timetable == null) {
                log.error("未找到课次信息, lessonNo: {}, storeId: {}", lessonNo, storeId);
                return R.ok("操作成功");
            }

            // 2. 构建课程信息
            StudentAttendanceNotificationService.CourseInfo courseInfo =
                new StudentAttendanceNotificationService.CourseInfo(
                    timetable.getCourseId(),
                    timetable.getLessonOrder(),
                    timetable.getCourseType(),
                    timetable.getClassStartDateTime(),
                    timetable.getClassEndDateTime(),
                    lessonNo
                );

            // 3. 构建通知上下文
            StudentAttendanceNotificationService.AttendanceNotificationContext context =
                new StudentAttendanceNotificationService.AttendanceNotificationContext(
                    lessonNo,
                    storeId,
                    studentsToNotify,
                    studentOpenIdMap,
                    StudentAttendanceNotificationService.NotificationType.REGULAR_COURSE,
                    courseInfo
                );

            // 4. 使用通用通知服务发送通知
            return studentAttendanceNotificationService.sendAttendanceNotifications(context);

        } catch (Exception e) {
            log.error("保存记录并发送通知失败, lessonNo: {}, storeId: {}", lessonNo, storeId, e);
            return R.ok("操作成功");
        }
    }

    /**
     * 异步发送微信通知
     *
     * @param messageContents 消息内容列表
     * @param lessonNo 课次编号
     * @param storeId 门店ID
     */
    @Async
    public void sendWxNotificationsAsync(List<String> messageContents, Long lessonNo, Long storeId) {
        try {
            log.info("开始异步发送{}条微信通知, lessonNo: {}, storeId: {}",
                messageContents.size(), lessonNo, storeId);

            readingPartyProxyService.sendPublicAccountMessage(messageContents);

            log.info("异步发送微信通知完成, lessonNo: {}, storeId: {}, 发送数量: {}",
                lessonNo, storeId, messageContents.size());

        } catch (Exception e) {
            log.error("异步发送微信通知失败, lessonNo: {}, storeId: {}, 消息数量: {}, error: {}",
                lessonNo, storeId, messageContents.size(), e.getMessage(), e);
        }
    }

    /**
     * 发送通知给学生列表
     *
     * @param lessonNo 课次编号
     * @param storeId 门店ID
     * @param studentsToNotify 需要通知的学生列表
     * @param studentOpenIdMap 学生ID到openId的映射
     * @return 发送结果
     */
    private R sendNotificationsToStudents(Long lessonNo, Long storeId,
                                         List<StudentCheckInInfoVO> studentsToNotify,
                                         Map<Long, String> studentOpenIdMap) {
        try {
            // 1. 获取课次信息
            Timetable timetable = timetableMapper.selectOne(
                Wrappers.<Timetable>lambdaQuery()
                    .eq(Timetable::getLessonNo, lessonNo)
                    .eq(Timetable::getStoreId, storeId)
            );

            if (timetable == null) {
                log.error("未找到课次信息, lessonNo: {}, storeId: {}", lessonNo, storeId);
                return R.failed("未找到课次信息");
            }

            // 2. 准备通知消息和记录
            List<String> messageContents = new ArrayList<>();
            List<WxStudentMsg> wxStudentMsgList = new ArrayList<>();

            for (StudentCheckInInfoVO student : studentsToNotify) {
                String openId = studentOpenIdMap.get(student.getStudentId());
                if (StringUtils.isBlank(openId)) {
                    continue;
                }

                // 构建微信消息模板
                WxMsgTplDto wxMsgTplDto = buildWxMsgTemplate(timetable, student, openId);
                String messageContent = JSON.toJSONString(wxMsgTplDto);
                messageContents.add(messageContent);

                // 构建数据库记录
                WxStudentMsg wxStudentMsg = buildWxStudentMsgRecord(timetable, student, openId, messageContent);
                wxStudentMsgList.add(wxStudentMsg);
            }

            if (messageContents.isEmpty()) {
                log.warn("没有有效的通知消息");
                return R.failed("没有有效的通知消息");
            }

            // 3. 发送通知
            log.info("开始发送{}条通知消息", messageContents.size());
            readingPartyProxyService.sendPublicAccountMessage(messageContents);

            // 4. 保存发送记录
            wxStudentMsgService.saveBatch(wxStudentMsgList);

            log.info("成功发送{}条通知消息", messageContents.size());
            return R.ok("成功发送" + messageContents.size() + "条通知消息");

        } catch (Exception e) {
            log.error("发送通知消息失败: {}", e.getMessage(), e);
            throw new BizException("发送通知消息失败: " + e.getMessage());
        }
    }

    /**
     * 构建微信消息模板
     *
     * @param timetable 课次信息
     * @param student 学生信息
     * @param openId 微信openId
     * @return 微信消息模板
     */
    private WxMsgTplDto buildWxMsgTemplate(Timetable timetable, StudentCheckInInfoVO student, String openId) {
        WxMsgTplDto wxMsgTplDto = new WxMsgTplDto();

        // 获取课程名称 - 使用RemoteLessonService获取真实的课节名称
        String courseName = getLessonName(timetable);

        // 格式化上课时间
        String classTime = timetable.getClassStartDateTime().toString();

        // 课消类型 - 使用RemoteCourseTypeService获取真实的课程类型名称
        String consumptionType = getCourseTypeName(timetable);

        // 消课课时 - 默认为1课时，可以根据实际业务调整
        String consumedHours = "1";

        // 构建消息内容 - 根据您提供的微信模板字段
        DataContent dataContent = DataContent.builder()
            .thing17(ValueContent.builder()  // 学员姓名
                .value(student.getStudentName())
                .build())
            .time8(ValueContent.builder()    // 上课时间
                .value(classTime)
                .build())
            .thing2(ValueContent.builder()   // 课程名称
                .value(courseName)
                .build())
            .phrase13(ValueContent.builder() // 课消类型
                .value(consumptionType)
                .build())
            .character_string18(ValueContent.builder() // 消课课时
                .value(consumedHours)
                .build())
            .build();

        wxMsgTplDto.setTouser(openId);
        wxMsgTplDto.setTemplate_id(jwBusinessConfig.getWxMsgCourseDepleteTplId());
        wxMsgTplDto.setData(dataContent);

        return wxMsgTplDto;
    }

    /**
     * 构建微信学生消息记录
     *
     * @param timetable 课次信息
     * @param student 学生信息
     * @param openId 微信openId
     * @param messageContent 消息内容
     * @return 微信学生消息记录
     */
    private WxStudentMsg buildWxStudentMsgRecord(Timetable timetable, StudentCheckInInfoVO student,
                                               String openId, String messageContent) {
        WxStudentMsg wxStudentMsg = new WxStudentMsg();

        wxStudentMsg.setAppName(WX_APP_NAME);
        wxStudentMsg.setSchoolId(StoreContextHolder.getSchoolId());
        wxStudentMsg.setStoreId(timetable.getStoreId());
        wxStudentMsg.setStudentId(student.getStudentId());
        wxStudentMsg.setObjId(timetable.getLessonNo());
        wxStudentMsg.setObjType(timetable.getCourseType());
        wxStudentMsg.setClassStartTime(timetable.getClassStartDateTime());
        wxStudentMsg.setType(WX_MSG_TYPE_SEND_TO_USER);
        wxStudentMsg.setRepType(WX_MSG_REP_TYPE_TEXT);
        wxStudentMsg.setRepEvent(WX_MSG_REP_EVENT_COURSE_REMINDER);
        wxStudentMsg.setContent(messageContent);
        wxStudentMsg.setSendStatus(WX_MSG_SEND_STATUS_SENDED);
        wxStudentMsg.setReadFlag(WX_MSG_READ_FLAG_UNREAD);
        wxStudentMsg.setAppId(WX_APP_ID);
        wxStudentMsg.setOpenId(openId);

        return wxStudentMsg;
    }

    /**
     * 获取课节名称
     *
     * @param timetable 课次信息
     * @return 课节名称
     */
    private String getLessonName(Timetable timetable) {
        try {
            // 构建查询参数
            LessonOrderDTO lessonOrderDTO = new LessonOrderDTO();
            lessonOrderDTO.setCourseId(timetable.getCourseId());
            lessonOrderDTO.setLessonOrderList(Collections.singletonList(timetable.getLessonOrder()));

            // 调用远程服务获取课节信息
            R<List<LessonVO>> lessonResult = remoteLessonService.getLessonListByOrder(
                Collections.singletonList(lessonOrderDTO));

            if (lessonResult.isOk() && CollUtil.isNotEmpty(lessonResult.getData())) {
                String lessonName = lessonResult.getData().get(0).getLessonName();
                log.info("获取课节名称成功: {}", lessonName);
                return lessonName;
            } else {
                log.warn("获取课节名称失败或为空, result: {}", lessonResult);
            }
        } catch (Exception e) {
            log.error("获取课节名称异常, courseId: {}, lessonOrder: {}",
                timetable.getCourseId(), timetable.getLessonOrder(), e);
        }

        // 如果获取失败，使用默认格式
        return "第" + timetable.getLessonOrder() + "节课";
    }

    /**
     * 获取课程类型名称
     *
     * @param timetable 课次信息
     * @return 课程类型名称
     */
    private String getCourseTypeName(Timetable timetable) {
        try {
            // 1. 通过课程ID获取课程信息，获取课程类型ID
            R<Map<Long, CourseVO>> courseResult = remoteCourseService.getCourseMapByIdList(
                Collections.singletonList(timetable.getCourseId()));

            if (!courseResult.isOk() || CollUtil.isEmpty(courseResult.getData())) {
                log.warn("获取课程信息失败或为空, courseId: {}, result: {}",
                    timetable.getCourseId(), courseResult);
                return "常规课消";
            }

            CourseVO courseVO = courseResult.getData().get(timetable.getCourseId());
            if (courseVO == null || courseVO.getCourseTypeId() == null) {
                log.warn("课程信息或课程类型ID为空, courseId: {}", timetable.getCourseId());
                return "常规课消";
            }

            Long courseTypeId = courseVO.getCourseTypeId();
            log.info("获取课程类型ID成功: courseId={}, courseTypeId={}",
                timetable.getCourseId(), courseTypeId);

            // 2. 调用远程服务获取所有课程类型
            R<List<CourseTypeDTO>> courseTypeResult = remoteCourseTypeService.getAll();
            if (courseTypeResult.isOk() && CollUtil.isNotEmpty(courseTypeResult.getData())) {
                Map<Long, String> courseTypeMap = courseTypeResult.getData().stream()
                    .collect(Collectors.toMap(
                        dto -> dto.getId().longValue(),
                        CourseTypeDTO::getName,
                        (a, b) -> a));

                String courseTypeName = courseTypeMap.get(courseTypeId);
                if (StringUtils.isNotBlank(courseTypeName)) {
                    log.info("获取课程类型名称成功: courseTypeId={}, courseTypeName={}",
                        courseTypeId, courseTypeName);
                    return courseTypeName;
                } else {
                    log.warn("未找到课程类型ID[{}]对应的名称", courseTypeId);
                }
            } else {
                log.warn("获取课程类型信息失败或为空, result: {}", courseTypeResult);
            }
        } catch (Exception e) {
            log.error("获取课程类型名称异常, courseId: {}", timetable.getCourseId(), e);
        }

        // 如果获取失败，使用默认值
        return "常规课消";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckInStudentVO getCheckInStudentByTimeableId(Serializable lessonno, Integer entryType,
        Long storeId) {
        Timetable timetable = timetableMapper.selectById(lessonno);
        if(Objects.nonNull(timetable)) {
            return getCheckInStudent(timetable.getLessonNo(), entryType, storeId);
        }
        return new CheckInStudentVO();
    }
}
