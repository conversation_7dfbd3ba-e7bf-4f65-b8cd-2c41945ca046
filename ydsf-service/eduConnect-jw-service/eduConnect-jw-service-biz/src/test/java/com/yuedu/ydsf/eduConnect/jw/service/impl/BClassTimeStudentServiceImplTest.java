package com.yuedu.ydsf.eduConnect.jw.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuedu.ydsf.eduConnect.api.constant.CheckInStatusEnum;
import com.yuedu.ydsf.eduConnect.api.constant.CourseTypeEnum;
import com.yuedu.ydsf.eduConnect.jw.entity.BClassTimeStudent;
import com.yuedu.ydsf.eduConnect.jw.entity.BCourseMakeUpOnline;
import com.yuedu.ydsf.eduConnect.jw.mapper.BCourseMakeUpOnlineMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * BClassTimeStudentServiceImpl 课时扣减优化测试
 */
@ExtendWith(MockitoExtension.class)
class BClassTimeStudentServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(BClassTimeStudentServiceImplTest.class);

    @Mock
    private BCourseMakeUpOnlineMapper bCourseMakeUpOnlineMapper;

    @InjectMocks
    private BClassTimeStudentServiceImpl bClassTimeStudentService;

    private Method checkStudentOnlineMakeUpDeductionMethod;

    @BeforeEach
    void setUp() throws Exception {
        // 通过反射获取私有方法用于测试
        checkStudentOnlineMakeUpDeductionMethod = BClassTimeStudentServiceImpl.class
            .getDeclaredMethod("checkStudentOnlineMakeUpDeduction", Long.class, Long.class, Long.class);
        checkStudentOnlineMakeUpDeductionMethod.setAccessible(true);
    }

    @Test
    void testCheckStudentOnlineMakeUpDeduction_NoMakeUpRecords() throws Exception {
        // 准备测试数据
        Long studentId = 1001L;
        Long lessonNo = 2001L;
        Long storeId = 3001L;

        // Mock: 没有线上补课记录
        when(bCourseMakeUpOnlineMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Collections.emptyList());

        // 执行测试
        Boolean result = (Boolean) checkStudentOnlineMakeUpDeductionMethod
            .invoke(bClassTimeStudentService, studentId, lessonNo, storeId);

        // 验证结果
        assertFalse(result, "没有线上补课记录时应返回false");
        
        // 验证调用
        verify(bCourseMakeUpOnlineMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
        
        log.info("测试通过：没有线上补课记录的场景");
    }

    @Test
    void testCheckStudentOnlineMakeUpDeduction_HasMakeUpButNotCheckedIn() throws Exception {
        // 准备测试数据
        Long studentId = 1001L;
        Long lessonNo = 2001L;
        Long storeId = 3001L;

        // Mock: 有线上补课记录
        BCourseMakeUpOnline makeUpOnline = new BCourseMakeUpOnline();
        makeUpOnline.setId(4001L);
        makeUpOnline.setLessonNo(lessonNo);
        makeUpOnline.setStoreId(storeId);
        makeUpOnline.setLessonOrder(1);

        when(bCourseMakeUpOnlineMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Arrays.asList(makeUpOnline));

        // Mock: 学生在线上补课中未签到
        BClassTimeStudentServiceImpl spyService = spy(bClassTimeStudentService);
        doReturn(0L).when(spyService).count(any(LambdaQueryWrapper.class));

        // 执行测试
        Boolean result = (Boolean) checkStudentOnlineMakeUpDeductionMethod
            .invoke(spyService, studentId, lessonNo, storeId);

        // 验证结果
        assertFalse(result, "学生在线上补课中未签到时应返回false");
        
        log.info("测试通过：有线上补课但学生未签到的场景");
    }

    @Test
    void testCheckStudentOnlineMakeUpDeduction_HasMakeUpAndCheckedIn() throws Exception {
        // 准备测试数据
        Long studentId = 1001L;
        Long lessonNo = 2001L;
        Long storeId = 3001L;

        // Mock: 有线上补课记录
        BCourseMakeUpOnline makeUpOnline = new BCourseMakeUpOnline();
        makeUpOnline.setId(4001L);
        makeUpOnline.setLessonNo(lessonNo);
        makeUpOnline.setStoreId(storeId);
        makeUpOnline.setLessonOrder(1);

        when(bCourseMakeUpOnlineMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenReturn(Arrays.asList(makeUpOnline));

        // Mock: 学生在线上补课中已签到
        BClassTimeStudentServiceImpl spyService = spy(bClassTimeStudentService);
        doReturn(1L).when(spyService).count(any(LambdaQueryWrapper.class));

        // 执行测试
        Boolean result = (Boolean) checkStudentOnlineMakeUpDeductionMethod
            .invoke(spyService, studentId, lessonNo, storeId);

        // 验证结果
        assertTrue(result, "学生在线上补课中已签到时应返回true");
        
        log.info("测试通过：学生已在线上补课中签到的场景");
    }

    @Test
    void testCheckStudentOnlineMakeUpDeduction_ExceptionHandling() throws Exception {
        // 准备测试数据
        Long studentId = 1001L;
        Long lessonNo = 2001L;
        Long storeId = 3001L;

        // Mock: 抛出异常
        when(bCourseMakeUpOnlineMapper.selectList(any(LambdaQueryWrapper.class)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试
        Boolean result = (Boolean) checkStudentOnlineMakeUpDeductionMethod
            .invoke(bClassTimeStudentService, studentId, lessonNo, storeId);

        // 验证结果：异常情况下应返回false，允许正常扣减流程
        assertFalse(result, "异常情况下应返回false，确保不影响正常业务流程");
        
        log.info("测试通过：异常处理场景");
    }

    @Test
    void testMakeUpLessonNoGeneration() {
        // 测试线上补课lessonNo的生成逻辑
        Long makeUpId = 4001L;
        Integer lessonOrder = 1;
        
        String expectedMakeUpLessonNo = CourseTypeEnum.COURSE_TYPE_ENUM_4.code
            + lessonOrder.toString()
            + makeUpId.toString();
        
        // 验证生成的lessonNo格式
        assertTrue(expectedMakeUpLessonNo.startsWith(String.valueOf(CourseTypeEnum.COURSE_TYPE_ENUM_4.code)),
            "线上补课lessonNo应以课程类型4开头");
        assertTrue(expectedMakeUpLessonNo.contains(lessonOrder.toString()),
            "线上补课lessonNo应包含课次序号");
        assertTrue(expectedMakeUpLessonNo.endsWith(makeUpId.toString()),
            "线上补课lessonNo应以补课记录ID结尾");
        
        log.info("测试通过：线上补课lessonNo生成逻辑, 生成的lessonNo: {}", expectedMakeUpLessonNo);
    }
}
