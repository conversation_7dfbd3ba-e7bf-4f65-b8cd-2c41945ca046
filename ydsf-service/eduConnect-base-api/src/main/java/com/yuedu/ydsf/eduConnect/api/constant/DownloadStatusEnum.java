package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 下载状态枚举
 * 
 * <AUTHOR>
 * @date 2024/11/01
 */
@AllArgsConstructor
public enum DownloadStatusEnum {
    /**
     * 不可用
     */
    DOWNLOAD_STATUS_0(0, "不可用"),
    /**
     * 可用
     */
    DOWNLOAD_STATUS_1(1, "可用"),
    /**
     * 错误
     */
    DOWNLOAD_STATUS_2(2, "错误");

    public final Integer code;

    public final String desc;
}