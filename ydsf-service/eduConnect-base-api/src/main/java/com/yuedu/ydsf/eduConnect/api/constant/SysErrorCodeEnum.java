package com.yuedu.ydsf.eduConnect.api.constant;

import com.yuedu.ydsf.common.core.exception.ErrorCode;

/**
 * @author: zhangchu<PERSON>fu
 * @date: 2024/10/21
 **/
public enum SysErrorCodeEnum  implements ErrorCode {
    /**
     * 直播间排课时间冲突
     */
    COURSE_SCHEDULE_CONFLICT("1501", "直播间排课时间冲突");

    /**
     * 错误码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;



    /**
     * @param code        错误码
     * @param description 描述
     */
    SysErrorCodeEnum(final String code, final String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
