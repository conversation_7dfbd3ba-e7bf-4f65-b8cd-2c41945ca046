package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 门店预约课程类型
 * <AUTHOR>
 * @date 2024/12/16 14:17
 */
@AllArgsConstructor
public enum CourseTypeEnum {

    /**
     * 直播课
     */
    COURSE_TYPE_ENUM_1(1,"直播课"),
    /**
     * 点播课
     */
    COURSE_TYPE_ENUM_2(2,"点播课"),
    /**
     * 补课
     */
    COURSE_TYPE_ENUM_3(3,"线下补课"),
    /**
     * 线上补课
     */
    COURSE_TYPE_ENUM_4(4,"线上补课");


    public final Integer code;

    public final String desc;

    /**
     * 获取课程类型描述
     */
    public static String getDescByCode(Integer code) {
        for (CourseTypeEnum courseTypeEnum : CourseTypeEnum.values()) {
            if (courseTypeEnum.code.equals(code)) {
                return courseTypeEnum.desc;
            }
        }
        return null;
    }
}
