package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 操作记录类型 枚举类
 * <AUTHOR>
 * @date 2024/11/4 15:56
 */
@AllArgsConstructor
public enum OperateTypeEnum {
    /**
     * 新增
     */
    OPERATE_TYPE_ENUM_1(1, "新增"),
    /**
     * 修改
     */
    OPERATE_TYPE_ENUM_2(2, "修改"),
    /**
     * 删除
     */
    OPERATE_TYPE_ENUM_3(3, "删除");

    public final Integer code;

    public final String desc;
}
