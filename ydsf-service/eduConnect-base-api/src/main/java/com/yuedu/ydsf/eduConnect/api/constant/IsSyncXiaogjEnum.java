package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * @author: zhangchuan<PERSON>
 * @date: 2024/10/08
 **/
@AllArgsConstructor
public enum IsSyncXiaogjEnum {
    /**
     * 否
     */
    IS_SYNC_XIAOGJ_ENUM_0(0, "否"),
    /**
     * 是
     */
    IS_SYNC_XIAOGJ_ENUM_1(1, "是");


    public final Integer CODE;

    public final String MSG;

    /**
     * 根据name获取code
     */
    public static Integer getCodeByName(String name) {
        for (IsSyncXiaogjEnum adTypeEnum : IsSyncXiaogjEnum.values()) {
            if (name.equals(adTypeEnum.MSG)) {
                return adTypeEnum.CODE;
            }
        }
        return null;
    }
}
