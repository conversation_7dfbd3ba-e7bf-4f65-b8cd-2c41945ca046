package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 课程状态枚举类
 * <AUTHOR>
 * @date 2024/10/11 11:45
 */
@AllArgsConstructor
public enum AttendClassStateEnum {

    /**
     * 进行中
     */
    ATTEND_CLASS_STATE_ENUM_0(0,"进行中"),
    /**
     * 即将开始
     */
    ATTEND_CLASS_STATE_ENUM_1(1,"即将开始"),
    /**
     * 本周内
     */
    ATTEND_CLASS_STATE_ENUM_2(2,"本周内"),
    /**
     * 未开始
     */
    ATTEND_CLASS_STATE_ENUM_3(3,"未开始"),
    /**
     * 已结束
     */
    ATTEND_CLASS_STATE_ENUM_4(4,"已结束"),
    /**
     * 进行中 + 未开始
     */
    ATTEND_CLASS_STATE_ENUM_5(5,"进行中 + 未开始");

    public final Integer CODE;

    public final String MSG;

}
