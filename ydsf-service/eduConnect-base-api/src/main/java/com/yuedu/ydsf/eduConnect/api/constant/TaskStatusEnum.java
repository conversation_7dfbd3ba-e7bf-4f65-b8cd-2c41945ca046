package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 录课任务状态
 * <AUTHOR>
 * @date 2025/1/16 11:39
 */
@AllArgsConstructor
public enum TaskStatusEnum {
    /**
     * 未完成
     */
    TASK_STATUS_ENUM_0(0, "未完成"),
    /**
     * 已完成
     */
    TASK_STATUS_ENUM_1(1, "已完成"),
    /**
     * 已取消
     */
    TASK_STATUS_ENUM_3(3, "已取消"),
    /**
     * 录制中
     */
    TASK_STATUS_ENUM_4(4, "录制中");

    public final Integer CODE;

    public final String MSG;
}
