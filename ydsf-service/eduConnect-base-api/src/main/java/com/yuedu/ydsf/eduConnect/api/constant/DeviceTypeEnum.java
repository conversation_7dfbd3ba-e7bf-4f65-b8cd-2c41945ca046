package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum DeviceTypeEnum {
    /**
     * 主讲端
     */
    DEVICETYPE_1(1, "直播端"),
    /**
     * 教室端
     */
    DEVICETYPE_2(2, "教室端"),

    /**
     * 助教端
     */
    DEVICETYPE_3(3, "讲师端"),

    /**
     * 监课端
     */
    DEVICETYPE_4(4, "监课端");

    public final Integer code;

    public final String desc;
}
