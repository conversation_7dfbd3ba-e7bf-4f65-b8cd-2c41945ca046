package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * TODO
 *
 * @author: KL
 * @date: 2024/11/04
 **/
@AllArgsConstructor
public enum AttendClassTimeStateEnum {
    /**
     * 进行中
     */
    ATTEND_CLASS_TIME_STATE_ENUM_0(0,"进行中"),
    /**
     * 即将开始
     */
    ATTEND_CLASS_TIME_STATE_ENUM_1(1,"即将开始"),
    /**
     * 本周内
     */
    ATTEND_CLASS_TIME_STATE_ENUM_2(2,"本周内"),
    /**
     * 未开始
     */
    ATTEND_CLASS_TIME_STATE_ENUM_3(3,"未开始"),
    /**
     * 已结束
     */
    ATTEND_CLASS_TIME_STATE_ENUM_4(4,"已结束"),
    /**
     * 进行中 + 未开始
     */
    ATTEND_CLASS_TIME_STATE_ENUM_5(5,"进行中 + 未开始");

    public final Integer code;

    public final String msg;
}
