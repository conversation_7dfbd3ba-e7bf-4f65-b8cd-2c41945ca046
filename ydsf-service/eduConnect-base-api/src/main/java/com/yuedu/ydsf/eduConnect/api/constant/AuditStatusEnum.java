package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 提交状态枚举
 * 
 * <AUTHOR>
 * @date 2024/11/01
 */
@AllArgsConstructor
public enum AuditStatusEnum {
    /**
     * 未提交
     */
    AUDIT_STATUS_0(0, "未提交"),

    /**
     * 待提交
     */
    AUDIT_STATUS_1(1, "待提交"),

    /**
     * 已提交
     */
    AUDIT_STATUS_2(2, "已提交"),

    /**
     * 回收站
     */
    AUDIT_STATUS_3(3, "回收站");

    public final Integer code;

    public final String desc;
}