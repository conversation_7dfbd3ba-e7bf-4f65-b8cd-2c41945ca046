package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 录制状态
 *
 * @author: KL
 * @date: 2024/10/10
 **/
@AllArgsConstructor
public enum RecordingStatusEnum {

    /**
     * 待录制
     */
    RECORDING_STATUS_0(0,"待录制"),
    /**
     * 录制中
     */
    RECORDING_STATUS_1(1,"录制中"),
    /**
     * 正常录制完成
     */
    RECORDING_STATUS_2(2,"正常录制完成"),
    /**
     * 录制作废（重新录制）
     */
    RECORDING_STATUS_3(3,"录制作废（重新录制）"),
    /**
     * 视频处理中
     */
    RECORDING_STATUS_4(4,"视频处理中"),
    /**
     * 视频处理失败
     */
    RECORDING_STATUS_5(5,"视频处理失败"),
    /**
     * 视频处理失败
     */
    RECORDING_STATUS_6(6,"停止录制"),
    /**
     * 转码中
     */
    RECORDING_STATUS_7(7,"转码中"),
    /**
     * 转码失败
     */
    RECORDING_STATUS_8(8,"转码失败");

    public final Integer code;

    public final String desc;
}
