package com.yuedu.ydsf.eduConnect.api.constant;

import lombok.AllArgsConstructor;

/**
 * 操作记录类别 枚举类
 * <AUTHOR>
 * @date 2024/11/4 15:56
 */
@AllArgsConstructor
public enum OperateCategoryEnum {
    /**
     * 店长红包自定义
     */
    CATEGORY_ENUM_1(1, "店长红包自定义"),

    /**
     * 打点记录
     */
    CATEGORY_ENUM_2(2, "打点记录"),

    /**
     * 点播库
     */
    CATEGORY_ENUM_3(3, "点播库"),

    /**
     * 设备管理
     */
    CATEGORY_ENUM_4(4, "设备管理"),

    /**
     * 班级管理
     */
    CATEGORY_ENUM_5(5, "班级管理"),

    /**
     * 调课管理
     */
    CATEGORY_ENUM_6(6, "调课管理"),

    /**
     * 结算周期
     */
    CATEGORY_ENUM_7(7, "结算周期");



    public final Integer code;

    public final String desc;
}
