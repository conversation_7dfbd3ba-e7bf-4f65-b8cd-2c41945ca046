package com.yuedu.ydsf.common.security.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;

import java.util.Map;
import java.util.Objects;

/**
 * PC端ContextHolder
 */
public class PcContextHolder {
    private static final Logger log = org.slf4j.LoggerFactory.getLogger(PcContextHolder.class);
    private static final ThreadLocal<Map<String, Object>> pcHolder = new TransmittableThreadLocal<>();

    private PcContextHolder() {
        throw new UnsupportedOperationException("这是一个工具类，不能被实例化！");
    }

    /**
     * 设置当前线程用户信息
     *
     * @param clientType 客户端类型（1:直播端、2:教室端、5:讲师端）
     * @param version    客户端版本号
     * @param deviceNo   设备号
     */
    public static void setPcContextHolder(Integer clientType, String version, String deviceNo,Long storeId) {
        log.debug("设置当前线程用户信息: clientType={}, version={}, deviceNo={} storeId={}", clientType, version, deviceNo,storeId);
        Map<String, Object> map = pcHolder.get();
        if (map == null) {
            map = new java.util.HashMap<>();
            pcHolder.set(map);
        }
        map.put("clientType", clientType);
        map.put("version", version);
        map.put("deviceNo", deviceNo);
        map.put("storeId",storeId);
    }

    /**
     * 获取PC端客户端类型
     */
    public static Integer getClientType() {
        if (MapUtils.isEmpty(pcHolder.get())) {
            return null;
        }
        Object clientType = pcHolder.get().get("clientType");
        log.debug("获取当前线程用户信息: clientType={}", clientType);
        return Objects.isNull(clientType) ? null : (Integer) clientType;
    }

    /**
     * 获取PC端版本号
     */
    public static String getVersion() {
        if (MapUtils.isEmpty(pcHolder.get())) {
            return null;
        }
        log.debug("获取当前线程用户信息: version={}", pcHolder.get().get("version"));
        return (String) pcHolder.get().get("version");
    }

    /**
     * 获取PC端设备号
     */
    public static String getDeviceNo() {
        if (MapUtils.isEmpty(pcHolder.get())) {
            return null;
        }
        log.debug("获取当前线程用户信息: deviceNo={}", pcHolder.get().get("deviceNo"));
        return (String) pcHolder.get().get("deviceNo");
    }

    public static Long getStoreId() {
        if (MapUtils.isEmpty(pcHolder.get())) {
            return null;
        }
        log.debug("获取当前线程用户信息: storeId={}", pcHolder.get().get("storeId"));
        return (Long) pcHolder.get().get("storeId");
    }


    /**
     * 清理当前线程用户信息
     */
    public static void clear() {
        log.debug("清理当前线程用户信息");
        pcHolder.remove();
    }

}
