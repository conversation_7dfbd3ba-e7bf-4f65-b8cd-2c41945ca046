package com.yuedu.ydsf.common.security.component;

import cn.hutool.core.util.ObjectUtil;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.vo.SsCampusVO;
import com.yuedu.ydsf.common.core.constant.SecurityConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.AccessDeniedException;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/10 08:30:08
 * <p>
 * 校区用户权限拦截器
 */
@Slf4j
@Aspect
@RequiredArgsConstructor
public class StorePermissionAspect implements Ordered {

    private final HttpServletRequest request;
    private final RemoteCampusService remoteCampusService;
    private final RedisTemplate<String, Object> redisTemplate;

    public static final String STORE_EMPLOYEE_CAMPUS = "new-store-employee-campus-%s";

    @SneakyThrows
    @Around("@within(storePermission) || @annotation(storePermission)")
    public Object around(ProceedingJoinPoint point, StorePermission storePermission) {
        String schoolId = request.getHeader(SecurityConstants.SCHOOL_ID);
        String storeId = request.getHeader(SecurityConstants.STORE_ID);
        if (storePermission == null) {
            Class<?> clazz = point.getTarget().getClass();
            //角色参数 权限控制 待定
            storePermission = AnnotationUtils.findAnnotation(clazz, StorePermission.class);
        }

        //获取 header 头 必要参数
        if (schoolId == null || storeId == null) {
            log.warn("访问接口 {} 没有权限", point.getSignature().getName());
            throw new AccessDeniedException("Access is denied");
        }
        if (!hasPermission(Long.parseLong(storeId))) {
            log.warn("访问接口 {} 没有数据权限", point.getSignature().getName());
            throw new AccessDeniedException("Data access is denied");
        }

        try {
            //添加全局数据 storeId schoolId
            StoreContextHolder.setStoreUserAttr(Long.parseLong(schoolId), Long.parseLong(storeId));
            return point.proceed();
        } finally {
            StoreContextHolder.clear();
        }
        //后续角色判断
        //storePermission.value()
    }

    private boolean hasPermission(Long storeId) {
        //getCampus列表中如何有id等于storeId 返回true 没有返回false
        List<SsCampusVO> campus = getCampus();
        if (ObjectUtil.isNull(campus)) {
            return false;
        }
        return getCampus().stream().anyMatch(vo -> vo.getId().equals(storeId));
    }


    public List<SsCampusVO> getCampus() {

        // 获取当前用户
        Long userId = SecurityUtils.getUser().getId();
        String key = String.format(STORE_EMPLOYEE_CAMPUS, userId);
        List<SsCampusVO> campusVo = List.of();
        try{
            campusVo = (List<SsCampusVO>) redisTemplate.opsForValue().get(key);
        }catch (Exception e) {
            redisTemplate.delete(key);
        }

        if (Objects.nonNull(campusVo)) {
            return campusVo;
        }

        AppUserQuery user = new AppUserQuery();
        user.setUserId(userId);

        try {
            // 调用远程服务获取校区信息
            R<List<SsCampusVO>> campusResult = remoteCampusService.getCampusInfoByUser(user);
            log.info("远程调用用户校区信息结果: {}", campusResult);

            if (!campusResult.isOk()) {
                log.error("获取用户校区信息失败, userId: {}, 错误信息: {}", userId, campusResult.getMsg());
                return null;
            }

            List<SsCampusVO> campusVO = campusResult.getData();
            if (campusVO == null) {
                log.warn("未查询到用户校区信息, userId: {}", userId);
                return null;
            }
            redisTemplate.opsForValue().set(key, new ArrayList<>(campusVO),5,TimeUnit.MINUTES);
            return campusVO;

        } catch (Exception e) {
            log.error("获取用户校区信息异常, campusId: {}", userId, e);
            return null;
        }
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 2;
    }

}
