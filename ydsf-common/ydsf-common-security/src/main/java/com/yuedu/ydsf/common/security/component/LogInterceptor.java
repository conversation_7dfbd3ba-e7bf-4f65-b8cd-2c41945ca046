package com.yuedu.ydsf.common.security.component;

import cn.hutool.core.text.CharSequenceUtil;
import com.yuedu.ydsf.common.core.util.UserContextHolder;
import com.yuedu.ydsf.common.security.service.YdsfUser;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import java.util.Objects;
import java.util.Optional;

public class LogInterceptor implements AsyncHandlerInterceptor {
    public static final String USER_TYPE = "user_type";
    public static final String USER_ID = "user_id";
    public static final String USER_NAME = "user_name";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // 匿名接口直接返回
        if (authentication instanceof AnonymousAuthenticationToken) {
            String username = UserContextHolder.getUserName();
            if (CharSequenceUtil.isNotBlank(username)) {
                MDC.put(USER_NAME, username);
            }
            return true;
        }

        if (Optional.ofNullable(authentication).isPresent()) {
            Object principal = authentication.getPrincipal();
            if (principal instanceof YdsfUser ydsfUser) {
                MDC.put(USER_TYPE, ydsfUser.getUserType());
                MDC.put(USER_ID, Objects.nonNull(ydsfUser.getId()) ? ydsfUser.getId().toString() : "");
                MDC.put(USER_NAME, ydsfUser.getName());
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        //调用结束后删除
        MDC.remove(USER_TYPE);
        MDC.remove(USER_ID);
        MDC.remove(USER_NAME);
    }
}
