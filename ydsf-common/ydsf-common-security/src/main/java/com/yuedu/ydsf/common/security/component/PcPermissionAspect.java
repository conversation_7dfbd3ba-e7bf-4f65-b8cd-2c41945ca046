package com.yuedu.ydsf.common.security.component;

import cn.hutool.core.util.ObjectUtil;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.UserContextHolder;
import com.yuedu.ydsf.common.security.annotation.PcPermission;
import com.yuedu.ydsf.common.security.util.PcContextHolder;
import com.yuedu.ydsf.eduConnect.api.constant.ActiveEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceArrearsEnum;
import com.yuedu.ydsf.eduConnect.api.constant.DeviceStateEnum;
import com.yuedu.ydsf.eduConnect.api.constant.SsDeviceConstant;
import com.yuedu.ydsf.eduConnect.live.api.feign.RemoteDeviceService;
import com.yuedu.ydsf.eduConnect.live.api.vo.SsDeviceVO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;


@Slf4j
@Aspect
public class PcPermissionAspect implements Ordered {
    private static final String X_DEVICE_NO = "X-Device-No";
    private static final String X_VERSION = "sfv";
    private static final String X_CLIENT_TYPE = "X-Client-Type";

    private final RedisTemplate<String, Object> redisTemplate;

    private final RemoteDeviceService remoteDeviceService;

    public PcPermissionAspect(RedisTemplate<String, Object> redisTemplate, RemoteDeviceService remoteDeviceService) {
        this.redisTemplate = redisTemplate;
        this.remoteDeviceService = remoteDeviceService;
    }

    @SneakyThrows
    @Around("@within(pcPermission) || @annotation(pcPermission)")
    public Object around(ProceedingJoinPoint point, PcPermission pcPermission) {
        log.info("PcPermissionAspect RedisTemplate KeySerializer: {}", redisTemplate.getKeySerializer().getClass().getName());
        log.info("PcPermissionAspect RedisTemplate HashKeySerializer: {}", redisTemplate.getHashKeySerializer().getClass().getName());
        log.info("PcPermissionAspect RedisTemplate ValueSerializer: {}", redisTemplate.getValueSerializer().getClass().getName());
        log.info("PcPermissionAspect RedisTemplate HashValueSerializer: {}", redisTemplate.getHashValueSerializer().getClass().getName());
        try {//获取 header 头 必要参数
            String deviceNo = getDeviceNoFromHeader();
            if (!hasPermission(deviceNo)) {
                log.warn("访问接口 {} 没有数据权限", point.getSignature().getName());
                throw new AccessDeniedException("Data access is denied");
            }
            return point.proceed();
        } finally {
            PcContextHolder.clear();
            UserContextHolder.clear();
        }
    }

    private boolean hasPermission(String deviceNo) {
        // 从缓存中获取设备信息
        SsDeviceVO deviceVO = getDeviceFromCache(deviceNo);
        if (Objects.isNull(deviceVO)) {
            throw new AccessDeniedException("设备信息不存在");
        }
        validateDeviceStatus(deviceVO);
        String clientType = getHeaderValue(X_CLIENT_TYPE);
        PcContextHolder.setPcContextHolder(Integer.valueOf(clientType), getHeaderValue(X_VERSION), deviceNo,deviceVO.getCampusId());
        // 线程存储用户名称
        UserContextHolder.setUserName(deviceVO.getDeviceName());
        return true;
    }


    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 3;
    }


    /**
     * 从请求头中获取设备码
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/9/29 14:48
     */
    private String getDeviceNoFromHeader() {
        String deviceNo = getHeaderValue(X_DEVICE_NO);
        return deviceNo;
    }

    /**
     * 缓存中获取设备信息
     *
     * @param deviceNo
     * @return com.yuedu.ydsf.eduConncet.api.vo.SsDeviceVO
     * <AUTHOR>
     * @date 2024/9/29 14:49
     */
    private SsDeviceVO getDeviceFromCache(String deviceNo) {
        // 删除老项目中的设备缓存key
        redisTemplate.delete(String.format(SsDeviceConstant.SS_DEVICE_INFO_OLD_KEY, deviceNo));
        // 用新的key进行查找没有则缓存新的key
        String key = String.format(SsDeviceConstant.SS_DEVICE_INFO_NEW_KEY, deviceNo);
        Object value = redisTemplate.opsForValue().get(key);
        log.info("设备信息缓存数据：{}", Objects.nonNull(value) ? value.toString() : "null");
        SsDeviceVO deviceVO = (SsDeviceVO) redisTemplate.opsForValue().get(key);
        if (deviceVO == null) {
            R<SsDeviceVO> ssDeviceVOR = remoteDeviceService.getDeviceByDeviceNo(deviceNo);
            if (ssDeviceVOR.isOk() && Objects.nonNull(ssDeviceVOR.getData())) {
                deviceVO = ssDeviceVOR.getData();
            } else {
                throw new AccessDeniedException("Access is denied");
            }
        }
        return deviceVO;
    }

    /**
     * 校验设备的状态
     *
     * @param deviceVO
     * @return void
     * <AUTHOR>
     * @date 2024/9/29 14:49
     */
    private void validateDeviceStatus(SsDeviceVO deviceVO) {

        if (ObjectUtil.equal(
                deviceVO.getDeviceActive(), String.valueOf(ActiveEnum.DEVICEACTIVE_0.code))) {
            throw new AccessDeniedException("设备未激活");
        }
        if (!ObjectUtil.equal(
                deviceVO.getDeviceArrears(), String.valueOf(DeviceArrearsEnum.DEVICEARREARS_0.code))) {
            throw new AccessDeniedException("设备已欠费");
        }
        if (ObjectUtil.equal(
                deviceVO.getDeviceState(), String.valueOf(DeviceStateEnum.DEVICESTATE_1.code))) {
            throw new AccessDeniedException("设备已禁用");
        }
        if (ObjectUtil.equal(deviceVO.getClassRoomId(), SsDeviceConstant.NOT_BIND)
                || ObjectUtil.isNull(deviceVO.getClassRoomId())) {
            throw new AccessDeniedException("设备未绑定");
        }
    }

    @NotNull
    private static String getHeaderValue(String headerName) {
        if (StringUtils.isEmpty(headerName)) {
            throw new AccessDeniedException("Access is denied");
        }
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new AccessDeniedException("Access is denied");
        }
        HttpServletRequest request = attributes.getRequest();
        String deviceNo = request.getHeader(headerName);
        if (deviceNo == null || deviceNo.isEmpty()) {
            throw new AccessDeniedException("Access is denied");
        }
        return deviceNo;
    }
}

