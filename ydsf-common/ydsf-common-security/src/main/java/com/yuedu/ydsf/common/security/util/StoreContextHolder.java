package com.yuedu.ydsf.common.security.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.yuedu.store.dto.StoreUserAttr;

/**
 * 存储上下文
 * <AUTHOR>
 */
public class StoreContextHolder {

    private static final ThreadLocal<StoreUserAttr> STORE_USER_ATTR = new TransmittableThreadLocal<>();

    // 添加私有构造函数，防止实例化
    private StoreContextHolder() {
        throw new UnsupportedOperationException("这是一个工具类，不能被实例化！");
    }

    public static void setStoreUserAttr(Long schoolId, Long storeId) {
        StoreUserAttr attr = new StoreUserAttr();
        attr.setSchoolId(schoolId);
        attr.setStoreId(storeId);
        STORE_USER_ATTR.set(attr);
    }

    public static Long getStoreId() {
        return getStoreUserAttrCheck().getStoreId();
    }

    public static Long getSchoolId() {
        return getStoreUserAttrCheck().getSchoolId();
    }


    public static void clear() {
        STORE_USER_ATTR.remove();
    }


    private static StoreUserAttr getStoreUserAttrCheck() {
        StoreUserAttr attr = STORE_USER_ATTR.get();
        if (attr == null) {
            throw new IllegalStateException("当前线程中未设置登录门店信息");
        }
        return attr;
    }
}
